// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {AmmalgamFactory} from 'contracts/factories/AmmalgamFactory.sol';
import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';
import {GeometricTWAP} from 'contracts/libraries/GeometricTWAP.sol';
import {MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG} from 'contracts/libraries/constants.sol';
import {Validation} from 'contracts/libraries/Validation.sol';
import {FactoryPairTestFixture, IPairHarness, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {
    adjustAmountsByTickPrice,
    computeExpectedSwapInAmountWithoutFees,
    computeExpectedSwapOutAmount,
    computeExpectedSwapOutAmountWithoutFees
} from 'test/shared/utilities.sol';
import {DEFAULT_MID_TERM_INTERVAL} from 'contracts/libraries/constants.sol';

/**
 * @notice  Tests of GeometricTWAP integration fuzz tests with pair contract.
 * @dev     Testing scenarios:
 *              Compute deposit Y for max borrow X with missing blocks
 */
contract GeometricTWAPInteractionFuzzTests is Test {
    using GeometricTWAP for GeometricTWAP.Observations;

    IAmmalgamFactory private factory;
    IPairHarness private pair;
    address private tester;
    address private random;

    FactoryPairTestFixture private fixture;

    uint8 private constant MAX_BORROW_PERCENTAGE = 90;

    uint256 private constant initialX = 80e18;
    uint256 private constant initialY = 20e18;
    uint256 private referenceReserveX;
    uint256 private referenceReserveY;
    uint8 private constant LTV = 75;

    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        factory = fixture.factory();
        pair = fixture.pair();

        tester = address(1111);
        random = address(99);

        fixture.transferTokensTo(random, initialX, initialY);
        fixture.mintForAndInitializeBlocks(random, initialX, initialY);
        (referenceReserveX, referenceReserveY) = pair.referenceReserves();
    }

    /**
     * @dev     This is a sample test to verify proper interaction with the pair contract.
     *          All comprehensive feature tests are conducted in GeometricTWAPValidationTests.sol.
     * @param   missingBlocks  fuzz test parameter to simulate different missing blocks.
     * @param   differentInterval  fuzz test parameter to simulate different intervals.
     */
    function test_longTermBlockIntervalWithMissingBlockFuzz(
        uint24 differentInterval,
        uint256 missingBlocks,
        uint128 borrowX,
        uint112 swapYIn
    ) public {
        {
            // this limit is due to multiplying it by `8` adjustment of the interval
            // and dividing by `14 * DEFAULT_MID_TERM_INTERVAL` to account for `MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG`
            uint256 maxLongTermIntervalBound = uint256(uint24(type(int24).max))
                / GeometricTWAP.LONG_TERM_ARRAY_LAST_INDEX / MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG;
            differentInterval = uint24(bound(differentInterval, 1, maxLongTermIntervalBound));

            // reset interval with different value
            fixture.configLongTermInterval(differentInterval * MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG);

            // setup bound for missingBlocks but not exceed differentInterval
            missingBlocks = bound(missingBlocks, 0, uint256(differentInterval));
        }

        // currently this borrowAdjustmentFactor could be increased/decrease randomly with different fuzz patterns to be able to pass the test.
        // after applying activeLiquidityScalerInQ72, the borrowAdjustmentFactor randomly fails the test with different swap patterns.
        // But 1005 seems handles all swap patterns. will find out later.
        uint256 borrowAdjustmentFactor = 1005;

        /*
         * Limit the size of the swap in to make sure it doesn't overflow
         * reserves which are of type uint112. Two swaps are made in
         * `generateReservesChangeWithTwoSwaps` so we subtract the initial
         * reserves from the uint112 max and divide by 2 to get the most we can
         * swap in twice.
         */
        swapYIn = uint112(bound(swapYIn, 1, (type(uint112).max - initialY) / 2));

        {
            (uint256 reserveX, uint256 reserveY) = generateReservesChangeWithTwoSwaps(swapYIn, missingBlocks);

            fixture.newBlock(1);
            pair.sync();

            // use minTick for negative X
            (int16 minTick,) = pair.exposed_getTickRange();

            (uint256 reserveXOut, uint256 reserveYIn) =
                adjustAmountsByTickPrice(reserveX, reserveY, reserveX, reserveY, minTick);
            {
                /*
                 * The maximum allowed to borrow of the adjusted `reserveXOut`
                 *  We only allow the borrow to cross 100 ticks, each tick is an increase in sqrt
                 *  price of `B`. X_1/X = sqrtP_1 / sqrt_P.
                 */
                uint256 borrowLessThanReserveAdjustedByTickPrice = reserveXOut - 1;

                /*
                 * The 90% is the maximum allowed borrow of actual, unadjusted, `reservesX`.
                 */
                uint256 borrowLessThanMaxBorrowLessAdjustmentFactor =
                    reserveX * MAX_BORROW_PERCENTAGE / 100 - borrowAdjustmentFactor;

                /*
                 * collateralY <= uint112.max
                 * collateralY * LTV / 100 <= uint112.max * LTV / 100
                 * collateralSwapped <= uint112.max * LTV / 100
                 * borrowLessThanOverflowYDeposit <=
                 *   computeExpectedSwapOutAmount(uint112.max * LTV / 100, reserveYIn, reserveXOut)
                 */
                uint256 borrowLessThanOverflowYDeposit =
                    computeExpectedSwapOutAmount(LTV * uint256(type(uint112).max) / 100, reserveYIn, reserveXOut);

                uint256 maxBorrow = Math.min(
                    borrowLessThanReserveAdjustedByTickPrice,
                    Math.min(borrowLessThanMaxBorrowLessAdjustmentFactor, borrowLessThanOverflowYDeposit)
                );

                vm.assume(maxBorrow > 1 + borrowAdjustmentFactor);

                borrowX = uint112(bound(borrowX, 1 + borrowAdjustmentFactor, maxBorrow));
            }

            uint256 collateralSwapped = computeExpectedSwapInAmountWithoutFees(borrowX, reserveYIn, reserveXOut);

            uint256 collateralY = Math.ceilDiv(100 * collateralSwapped, LTV);

            fixture.transferTokensTo(tester, 0, collateralY);
            fixture.depositFor(tester, 0, collateralY);

            vm.expectRevert(Validation.AmmalgamLTV.selector);
            fixture.borrowFor(tester, borrowX + borrowAdjustmentFactor, 0);

            fixture.borrowFor(tester, borrowX - borrowAdjustmentFactor, 0);
        }
    }

    function generateReservesChangeWithTwoSwaps(
        uint256 swapAmountYIn,
        uint256 missingBlocks
    ) private returns (uint256 updatedReserveX, uint256 updatedReserveY) {
        // 1st swap
        fixture.newBlock(1);

        uint256 swapAmountXOut =
            computeExpectedSwapOutAmount(swapAmountYIn, initialY, referenceReserveY, initialX, 0, 0);

        fixture.swapYInXOut(tester, swapAmountYIn, swapAmountXOut);

        uint256 reserveX;
        uint256 reserveY;

        (reserveX, reserveY,) = pair.getReserves();
        updatedReserveX = initialX - swapAmountXOut;
        updatedReserveY = initialY + swapAmountYIn;

        assertEq(reserveX, updatedReserveX);
        assertEq(reserveY, updatedReserveY);

        // 2nd swap
        // missing blocks between two swaps by just rolling number of missing blocks without calling _update()
        fixture.newBlock(missingBlocks);

        // make sure observation is updated before grabbing new referenceReserves
        pair.sync();

        (referenceReserveX, referenceReserveY) = pair.referenceReserves();

        swapAmountXOut =
            computeExpectedSwapOutAmount(swapAmountYIn, updatedReserveY, referenceReserveY, updatedReserveX, 0, 0);

        fixture.swapYInXOut(tester, swapAmountYIn, swapAmountXOut);

        updatedReserveX -= swapAmountXOut;
        updatedReserveY += swapAmountYIn;

        (reserveX, reserveY,) = pair.getReserves();

        assertEq(reserveX, updatedReserveX);
        assertEq(reserveY, updatedReserveY);
    }
}
