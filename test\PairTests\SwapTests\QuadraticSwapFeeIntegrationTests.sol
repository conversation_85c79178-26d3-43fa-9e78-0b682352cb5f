// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {AmmalgamPair} from 'contracts/AmmalgamPair.sol';
import {QuadraticSwapFees} from 'contracts/libraries/QuadraticSwapFees.sol';

import {FactoryPairTestFixture, IPairHarness, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {DepletedAssetUtils} from 'test/utils/DepletedAssetUtils.sol';

contract QuadraticSwapFeesTests is Test {
    IPairHarness private pair;
    address private pairAddress;
    FactoryPairTestFixture private fixture;

    uint256 private initialMintX;
    uint256 private initialMintY;
    uint256 private k;
    address private alice = address(123);
    address private bob = address(124);

    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        pair = fixture.pair();
        pairAddress = address(pair);

        initialMintX = 90e18;
        initialMintY = 10e18;
        k = initialMintX * initialMintY;

        // split the mint so its not in one address when borrowing liquidity.
        fixture.transferTokensTo(alice, initialMintX * 3 / 5, initialMintY * 3 / 5);
        fixture.mintFor(alice, initialMintX * 3 / 5, initialMintY * 3 / 5);
        fixture.transferTokensTo(bob, initialMintX * 2 / 5, initialMintY * 2 / 5);
        fixture.mintFor(bob, initialMintX * 2 / 5, initialMintY * 2 / 5);
    }

    function testSwapAfterMintAdjustsReferenceReserve() public {
        uint256 newMintX = initialMintX / 3;
        uint256 newMintY = initialMintY / 3;
        uint256 swapXIn = 1e18;

        (uint256 referenceReserveX, uint256 referenceReserveY) = pair.referenceReserves();

        uint256 expectedReferenceReserveX = referenceReserveX * (initialMintX + newMintX) / initialMintX;
        uint256 expectedReferenceReserveY = referenceReserveY * (initialMintY + newMintY) / initialMintY;

        uint256 expectedSwapYOut = DepletedAssetUtils.computeExpectedSwapOutAmount(
            swapXIn, initialMintX + newMintX, expectedReferenceReserveX, initialMintY + newMintY, 0, 0
        );

        fixture.transferTokensTo(alice, newMintX, newMintY);
        fixture.mintFor(alice, newMintX, newMintY);

        (uint256 actualReferenceReserveX, uint256 actualReferenceReserveY) = pair.referenceReserves();

        assertEq(actualReferenceReserveX, expectedReferenceReserveX, 'reserve should be adjusted by mint');
        assertEq(actualReferenceReserveY, expectedReferenceReserveY, 'reserve should be adjusted by mint');

        fixture.transferTokensTo(pairAddress, swapXIn, 0);
        vm.expectRevert(AmmalgamPair.K.selector);
        pair.swap(0, expectedSwapYOut + 1, bob, '');
        // Don't expect revert
        pair.swap(0, expectedSwapYOut, bob, '');
    }

    function testSwapAfterBurnAdjustsReferenceReserve() public {
        uint256 burnX = initialMintX / 4;
        uint256 burnY = initialMintY / 4;
        uint256 burnL = Math.sqrt(burnX * burnY);
        uint256 swapYIn = 1e18;

        (uint256 referenceReserveX, uint256 referenceReserveY) = pair.referenceReserves();
        uint256 expectedReferenceReserveX = referenceReserveX * (initialMintX - burnX) / initialMintX;
        uint256 expectedReferenceReserveY = referenceReserveY * (initialMintY - burnY) / initialMintY;

        uint256 expectedSwapXOut = DepletedAssetUtils.computeExpectedSwapOutAmount(
            swapYIn, initialMintY - burnY, expectedReferenceReserveY, initialMintX - burnX, 0, 0
        );

        fixture.burnFor(alice, burnL);

        (uint256 actualReferenceReserveX, uint256 actualReferenceReserveY) = pair.referenceReserves();
        assertEq(actualReferenceReserveX, expectedReferenceReserveX, 'reserve should be adjusted by burn');
        assertEq(actualReferenceReserveY, expectedReferenceReserveY, 'reserve should be adjusted by burn');

        fixture.transferTokensTo(pairAddress, 0, swapYIn);
        vm.expectRevert(AmmalgamPair.K.selector);
        pair.swap(expectedSwapXOut + 1, 0, bob, '');
        // Don't expect revert
        pair.swap(expectedSwapXOut, 0, bob, '');
    }

    function testSwapAfterBorrowLiquidityAdjustsReferenceReserve() public {
        uint256 borrowLX = initialMintX / 5;
        uint256 borrowLY = initialMintY / 5;
        uint256 borrowL = Math.sqrt(borrowLX * borrowLY);
        uint256 swapXIn = 1e18;

        (uint256 referenceReserveX, uint256 referenceReserveY) = pair.referenceReserves();
        uint256 expectedReferenceReserveX = referenceReserveX * (initialMintX - borrowLX) / initialMintX;
        uint256 expectedReferenceReserveY = referenceReserveY * (initialMintY - borrowLY) / initialMintY;

        uint256 expectedSwapYOut = DepletedAssetUtils.computeExpectedSwapOutAmount(
            swapXIn, initialMintX - borrowLX, expectedReferenceReserveX, initialMintY - borrowLY, 0, 0
        );

        fixture.borrowLiquidityFor(alice, borrowL);

        (uint256 actualReferenceReserveX, uint256 actualReferenceReserveY) = pair.referenceReserves();
        assertEq(actualReferenceReserveX, expectedReferenceReserveX, 'reserve should be adjusted by borrow');
        assertEq(actualReferenceReserveY, expectedReferenceReserveY, 'reserve should be adjusted by borrow');

        fixture.transferTokensTo(pairAddress, swapXIn, 0);
        vm.expectRevert(AmmalgamPair.K.selector);
        pair.swap(0, expectedSwapYOut + 1, bob, '');
        // Don't expect revert
        pair.swap(0, expectedSwapYOut, bob, '');
    }

    function testSwapAfterRepayLiquidityAdjustsReferenceReserve() public {
        uint256 borrowLX = initialMintX / 5;
        uint256 borrowLY = initialMintY / 5;
        uint256 repayLX = initialMintX / 6;
        uint256 repayLY = initialMintY / 6;
        uint256 swapYIn = 1e18;

        (uint256 referenceReserveX, uint256 referenceReserveY) = pair.referenceReserves();
        uint256 expectedReferenceReserveX = referenceReserveX * (initialMintX - borrowLX + repayLX) / initialMintX;
        uint256 expectedReferenceReserveY = referenceReserveY * (initialMintY - borrowLY + repayLY) / initialMintY;

        uint256 expectedSwapXOut = DepletedAssetUtils.computeExpectedSwapOutAmount(
            swapYIn,
            initialMintY - borrowLY + repayLY,
            expectedReferenceReserveY,
            initialMintX - borrowLX + repayLX,
            0,
            0
        );

        fixture.borrowLiquidityFor(alice, Math.sqrt(borrowLX * borrowLY));
        fixture.repayLiquidityFor(alice, repayLX, repayLY, Math.sqrt(repayLX * repayLY));

        (uint256 actualReferenceReserveX, uint256 actualReferenceReserveY) = pair.referenceReserves();
        assertEq(actualReferenceReserveX, expectedReferenceReserveX, 'reserve should be adjusted by repay');
        assertEq(actualReferenceReserveY, expectedReferenceReserveY, 'reserve should be adjusted by repay');

        fixture.transferTokensTo(pairAddress, 0, swapYIn);
        vm.expectRevert(AmmalgamPair.K.selector);
        pair.swap(expectedSwapXOut + 1, 0, bob, '');
        // Don't expect revert
        pair.swap(expectedSwapXOut, 0, bob, '');
    }
}
