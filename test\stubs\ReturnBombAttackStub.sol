// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

contract ReturnBombAttackStub {
    function symbol() external pure returns (string memory) {
        return
        'Vjsufbeuqnvzkeiwalrphztoqdclmkyrixbohmrfenyqvwgdzilyfbknhajgptxdmowscubliekmfrtnjvhswzoqgdpxlrkcanmjvyfiehlnobtwzskrpaxmcdfguvoqhijynwzeltbpdmusqvkghxraoyijbcflntvwpzseukdmorhzayjiclfnbkvgpxqrtwmosdeunhajlzfbvkrphigqxwcytmnosjldabufkvhxpyetizrmqncfwugkpdaobhzqjrlysmievtwnfxgbuoclzjyphkarqeimcdntvsxgoyrfjuwblhzkpdmvxtqsiwenmcnfdbglzuvkrhojatpeywixszqofubldgknjmrychtvzqaowfjrbphdmxlzieksvtnygcquomwbrdjkpfhleizavqxsgoncytmuqrpwjhzdfbeikvnlysxgtcovrwamubplnzqeifdhkjmwtcyvoxrngqpbsduhezlkjyisxrcabowgfhpxetvndqumzlivdrkbnejswxgyqzfoacvjlprhkmtwbszvuyqocndliemftvnrpxhzsakwgubdqyeilcvrtmnfjsxohepqgwlzdnbkaumvyrhtijplscxfgowznbetqkvyudrmhxplswcniojagtdqzfbxvyeruokmhnlpdzmcigjqfboyhxktsdwuelanopigrjcvytbhfmklsueqdpwznvxcratoibyfjkmxgzdnhslopqeurvwqmtjcknyfhbzxdgiplatnsyuowrhmekljqxfzcpvbgdntoawrumyeqhsplkzvjcfxrgitdoqnbavykwhzlpxmsuecrjoigtqflwdnmbykxviuzprcajeosltfbghdwmtkqxucynizvaprelkfqsbndtmojyxhuicvwlnzoxgprmjaxdsyklfebwznhocgvuripmqejyfzxadtkwcbsgolhrnmvyjplufwbektaxrnjhzvqydmgowtseikflbuzqnyjhpwrdamvcgofxulkbtpznivqsyewrmocuhdxalpjkztfgievbnyqswdhcoaplmtruikxjzvbfwqyehgncsdlropkmuxizvtyfenbdwhjrqklmsctpxougyiafhznvkrjolqebduwtspzyxmgnvhkbwrfcljotyxupzidqsmegnhlbafivkrwptdmouxjyczhqlnkbgeivrjatpywdsxzmfcohuqnepltvykxsiwmrdqzjogbahfcul';
    }
}
