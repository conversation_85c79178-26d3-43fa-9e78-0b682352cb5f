// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';

import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {DEPOSIT_X, DEPOSIT_Y, BORROW_X, BORROW_Y} from 'contracts/interfaces/tokens/ITokenController.sol';

import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';

contract RepayTests is Test {
    IAmmalgamFactory private factory;
    IAmmalgamPair private pair;
    address private pairAddress;

    address private tester;
    address private tester2;
    address private tester3;

    FactoryPairTestFixture private fixture;

    mapping(address => uint256) private initialBalanceX;
    mapping(address => uint256) private initialBalanceY;

    struct SlotVars {
        uint256 mintX;
        uint256 mintY;
        uint256 borrowX;
        uint256 borrowY;
        uint256 borrowL;
        uint256 depositX;
        uint256 depositY;
        uint256 repayX;
        uint256 repayY;
    }

    error ERC20InsufficientBalance(address sender, uint256 balance, uint256 needed);

    function setUp() public {
        tester = vm.addr(1111);
        tester2 = vm.addr(1112);
        tester3 = vm.addr(1113);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        factory = fixture.factory();
        pair = fixture.pair();
        pairAddress = address(pair);

        initialBalanceX[tester] = 5000e18;
        initialBalanceY[tester] = 5000e18;
        initialBalanceX[tester2] = 5000e18;
        initialBalanceY[tester2] = 5000e18;
        initialBalanceX[tester3] = 5000e18;
        initialBalanceY[tester3] = 5000e18;

        fixture.transferTokensTo(tester, initialBalanceX[tester], initialBalanceX[tester]).transferTokensTo(
            tester2, initialBalanceX[tester2], initialBalanceX[tester2]
        ).transferTokensTo(tester3, initialBalanceX[tester3], initialBalanceX[tester3]);
    }

    function testRepayYInFull() public {
        SlotVars memory slot;

        slot.mintX = 2e18;
        slot.mintY = 8e18;

        slot.depositX = 8e18;
        slot.borrowY = 1e18;
        slot.repayY = 1e18;

        verifyRepayXY(tester, slot);
    }

    function testRepayXInFull() public {
        SlotVars memory slot;

        slot.mintX = 2e18;
        slot.mintY = 8e18;
        slot.borrowX = 1e18;
        slot.depositY = 8e18;
        slot.repayX = 1e18;

        verifyRepayXY(tester, slot);
    }

    function testRepayLess() public {
        SlotVars memory slot;

        slot.mintX = 2e18;
        slot.mintY = 8e18;

        slot.depositX = 8e18;
        slot.borrowY = 2e18;
        slot.repayY = 1e18;

        verifyRepayXY(tester, slot);
    }

    function testRepayMore() public {
        SlotVars memory slot;

        slot.mintX = 2e18;
        slot.mintY = 8e18;

        slot.depositX = 8e18;
        slot.borrowY = 2e18;
        slot.repayY = 3e18;

        initialDepositAndBorrow(tester, slot);

        vm.expectRevert(
            abi.encodeWithSelector(ERC20InsufficientBalance.selector, tester, slot.borrowY = 2e18, slot.repayY)
        );
        fixture.repayForNoEvent(tester, slot.repayX, slot.repayY);
    }

    function testRepayThenWithdraw() public {
        SlotVars memory slot;

        slot.mintX = 2e18;
        slot.mintY = 8e18;
        slot.borrowY = 1e18;
        slot.depositX = 8e18;
        slot.repayY = 1e18;

        verifyRepayXY(tester, slot);

        verifyWithdraw(tester, slot);
    }

    function testRepayWithoutDebt_Revert() public {
        SlotVars memory slot;
        slot.mintX = 2e18;
        slot.mintY = 8e18;
        slot.borrowY = 1e18;

        // initialize a borrow by tester to avoid blowing out the failure in AmmalgamERC20.sol  _beforeTokenTransfer
        // for "AmmalgamERC20: burn amount exceeds totalSupply"
        initialDepositAndBorrow(tester, slot);

        uint256 repayAmountXForTester2WhoHasNoDebt = 0;
        uint256 repayAmountYForTester2WhoHasNoDebt = 2;

        vm.expectRevert(
            abi.encodeWithSelector(
                ERC20InsufficientBalance.selector,
                tester2,
                repayAmountXForTester2WhoHasNoDebt,
                repayAmountYForTester2WhoHasNoDebt
            )
        );
        fixture.repayForNoEvent(tester2, repayAmountXForTester2WhoHasNoDebt, repayAmountYForTester2WhoHasNoDebt);
    }

    function testRepayAndSwap() public {
        SlotVars memory slot;

        slot.mintX = 2e18;
        slot.mintY = 8e18;
        slot.borrowY = 1e18;
        slot.depositX = 8e18;
        slot.repayY = 1e18;

        initialDepositAndBorrow(tester, slot);

        uint256 swapAmount = 1e18;
        (uint256 amountOut,,) = fixture.verifySwapXToY(tester, swapAmount, slot.mintX, slot.mintY);

        fixture.repayFor(tester, slot.repayX, slot.repayY);

        // after swap the pool balance has been changed.
        slot.mintX += swapAmount;
        slot.mintY -= amountOut;

        assertionRepay(tester, slot);
    }

    function verifyWithdraw(address _tester, SlotVars memory slot) private {
        vm.startPrank(_tester);

        uint256 testerDepositX = pair.tokens(DEPOSIT_X).balanceOf(_tester);
        uint256 testerDepositY = pair.tokens(DEPOSIT_Y).balanceOf(_tester);

        uint256 testerBalanceX = fixture.tokenX().balanceOf(_tester);
        uint256 testerBalanceY = fixture.tokenY().balanceOf(_tester);

        uint256 pairBalanceX = fixture.tokenX().balanceOf(address(pair));
        uint256 pairBalanceY = fixture.tokenY().balanceOf(address(pair));

        vm.stopPrank();

        if (slot.depositX > testerDepositX || slot.depositY > testerDepositY) {
            vm.expectRevert('Ammalgam: INSUFFICIENT_BALANCE');
            fixture.withdrawFor(_tester, slot.depositX, slot.depositY);
            return;
        } else {
            fixture.withdrawFor(_tester, slot.depositX, slot.depositY);
        }

        assertEq(pair.tokens(DEPOSIT_X).balanceOf(_tester), testerDepositX - slot.depositX);
        assertEq(pair.tokens(DEPOSIT_Y).balanceOf(_tester), testerDepositY - slot.depositY);

        assertEq(fixture.tokenX().balanceOf(address(pair)), pairBalanceX - slot.depositX);
        assertEq(fixture.tokenY().balanceOf(address(pair)), pairBalanceY - slot.depositY);

        assertEq(fixture.tokenX().balanceOf(_tester), testerBalanceX + slot.depositX);
        assertEq(fixture.tokenY().balanceOf(_tester), testerBalanceY + slot.depositY);
    }

    function verifyRepayXY(address _tester, SlotVars memory slot) private {
        initialDepositAndBorrow(_tester, slot);

        fixture.repayFor(_tester, slot.repayX, slot.repayY);

        assertionRepay(_tester, slot);
    }

    function initialDepositAndBorrow(address _tester, SlotVars memory slot) private {
        fixture.mintForAndInitializeBlocks(_tester, slot.mintX, slot.mintY);
        fixture.depositFor(_tester, slot.depositX, slot.depositY);
        fixture.borrowFor(_tester, slot.borrowX, slot.borrowY);
    }

    function assertionRepay(address _tester, SlotVars memory slot) private {
        vm.startPrank(_tester);

        uint256 testerBorrowXBalance = slot.borrowX - slot.repayX;
        uint256 testerBorrowYBalance = slot.borrowY - slot.repayY;

        uint256 pairBalanceX = slot.mintX - slot.borrowX + slot.repayX + slot.depositX;
        uint256 pairBalanceY = slot.mintY - slot.borrowY + slot.repayY + slot.depositY;

        uint256 testerBalanceX = initialBalanceX[_tester] - slot.mintX + slot.borrowX - slot.repayX - slot.depositX;
        uint256 testerBalanceY = initialBalanceY[_tester] - slot.mintY + slot.borrowY - slot.repayY - slot.depositY;

        assertEq(pair.tokens(BORROW_X).balanceOf(_tester), testerBorrowXBalance);
        assertEq(pair.tokens(BORROW_Y).balanceOf(_tester), testerBorrowYBalance);

        assertEq(pair.tokens(DEPOSIT_X).balanceOf(_tester), slot.depositX);
        assertEq(pair.tokens(DEPOSIT_Y).balanceOf(_tester), slot.depositY);

        assertEq(fixture.tokenX().balanceOf(address(pair)), pairBalanceX);
        assertEq(fixture.tokenY().balanceOf(address(pair)), pairBalanceY);

        assertEq(fixture.tokenX().balanceOf(_tester), testerBalanceX);
        assertEq(fixture.tokenY().balanceOf(_tester), testerBalanceY);

        (uint112 _reserveX, uint112 _reserveY,) = pair.getReserves();
        assertEq(_reserveX, slot.mintX);
        assertEq(_reserveY, slot.mintY);

        vm.stopPrank();
    }
}
