// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';

contract DepletedAssetDepositTests is Test {
    IAmmalgamPair private pair;
    address private pairAddress;

    address private tester;

    FactoryPairTestFixture private fixture;

    error ERC20InsufficientBalance(address sender, uint256 balance, uint256 needed);

    uint256 private initialMintX;
    uint256 private initialMintY;
    uint256 private missingXAssets;
    uint256 private missingYAssets;
    uint256 private initialLiquidity;
    uint256 private constant BUFFER = 95;

    function setUp() public {
        tester = address(1111);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        pair = fixture.pair();
        pairAddress = address(pair);

        initialMintX = 4e18;
        initialMintY = 3e18;
        missingXAssets = 3e18;
        missingYAssets = 2e18;

        fixture.transferTokensTo(tester, initialMintX, initialMintY);
        initialLiquidity = fixture.mintForAndInitializeBlocks(tester, initialMintX, initialMintY) + 1000;
        fixture.borrowFor(tester, missingXAssets, missingYAssets);
    }

    function testDepletedAssetRepayXReplenishes() public {
        // move from (4, 3) to (3.15, 4 + fee) where x is depleted
        (, uint256 swapAmountXOut) = fixture.moveReservesLeftToXValue(tester, 3.15e18);

        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 repayX = 1e18;
        uint256 fromReserveX = 0.15e18;

        fixture.transferTokensTo(tester, repayX, 0);
        // repay 1x to replenish moves curve to (3, 4 + fee) and gives 0.15 extra to depositor
        fixture.repayFor(tester, repayX, 0, fromReserveX, 0);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + repayX,
                tokenY: reserveYBefore - missingYAssets,
                reserveXAssets: reserveXBefore - fromReserveX,
                reserveYAssets: reserveYBefore
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceX = missingXAssets + swapAmountXOut; // borrowed X + swapAmountXOut + repayX - repayX
        expectTester.balanceY = missingYAssets;
        expectTester.pairBorrowedX = missingXAssets - repayX - fromReserveX;
        expectTester.pairBorrowedY = missingYAssets;
        expectTester.pairLiquidity = initialLiquidity - 1000;

        fixture.verifyAddress(expectTester);
    }

    function testDepletedAssetPayoffDebtXWithReward() public {
        // move from (4, 3) to (3.15, 4 + fee) where x is depleted
        (, uint256 swapAmountXOut) = fixture.moveReservesLeftToXValue(tester, 3.15e18);

        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 fromReserveX = 0.15e18;
        uint256 repayX = missingXAssets - fromReserveX;

        fixture.transferTokensTo(tester, repayX, 0);

        fixture.repayFor(tester, repayX, 0, fromReserveX, 0);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + repayX,
                tokenY: reserveYBefore - missingYAssets,
                reserveXAssets: reserveXBefore - fromReserveX,
                reserveYAssets: reserveYBefore
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceX = missingXAssets + swapAmountXOut; // borrowed X + swapAmountXOut + repayX - repayX
        expectTester.balanceY = missingYAssets;
        expectTester.pairBorrowedX = 0; // missingXAssets - (repayX + fromReserveX);
        expectTester.pairBorrowedY = missingYAssets;
        expectTester.pairLiquidity = initialLiquidity - 1000;

        fixture.verifyAddress(expectTester);
    }

    function testDepletedAssetOverRepayXReplenishesFail() public {
        // move from (4, 3) to (3.15, 4 + fee) where x is depleted
        fixture.moveReservesLeftToXValue(tester, 3.15e18);

        uint256 fromReserveX = 0.15e18;
        uint256 repayX = missingXAssets - fromReserveX + 1;

        fixture.transferTokensTo(tester, repayX, 0);

        vm.expectRevert(
            abi.encodeWithSelector(ERC20InsufficientBalance.selector, tester, missingXAssets, missingXAssets + 1)
        );
        fixture.repayFor(tester, repayX, 0, fromReserveX, 0);
    }

    function testDepletedAssetRepayXPartiallyReplenishes() public {
        // move from (4, 3) to (3.1, 6 + fee) where x is depleted
        (, uint256 swapAmountXOut) = fixture.moveReservesLeftToXValue(tester, 3.1e18);

        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 repayX = 1e18;
        uint256 fromReserveX = repayX;

        fixture.transferTokensTo(tester, repayX, 0);
        // repay 1x to replenish moves curve to (2.1, 6 + fee) and gives 1 extra to repayer
        fixture.repayFor(tester, repayX, 0, fromReserveX, 0);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + repayX,
                tokenY: reserveYBefore - missingYAssets,
                reserveXAssets: reserveXBefore - fromReserveX,
                reserveYAssets: reserveYBefore
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceX = missingXAssets + swapAmountXOut; // borrowed X + swapAmountXOut + repayX - repayX
        expectTester.balanceY = missingYAssets;
        expectTester.pairBorrowedX = missingXAssets - repayX - fromReserveX;
        expectTester.pairBorrowedY = missingYAssets;
        expectTester.pairLiquidity = initialLiquidity - 1000;

        fixture.verifyAddress(expectTester);
    }

    function testDepletedAssetRepayYReplenishes() public {
        // move from (4, 3) to (6 + fee, 2.1) where y is depleted
        (, uint256 swapAmountYOut) = fixture.moveReservesRightToYValue(tester, 2.1e18);

        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 repayY = 1e18;
        uint256 fromReserveY = 0.1e18;

        fixture.transferTokensTo(tester, 0, repayY);
        // repay 1Y to replenish moves curve to (6 + fee, 2), repayer get reduced 0.1 extra from debt
        fixture.repayFor(tester, 0, repayY, 0, fromReserveY);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets,
                tokenY: reserveYBefore - missingYAssets + repayY,
                reserveXAssets: reserveXBefore,
                reserveYAssets: reserveYBefore - fromReserveY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceX = missingXAssets;
        expectTester.balanceY = missingYAssets + swapAmountYOut; // borrowed Y + swapAmountYOut + repayY - repayY
        expectTester.pairBorrowedX = missingXAssets;
        expectTester.pairBorrowedY = missingYAssets - repayY - fromReserveY;
        expectTester.pairLiquidity = initialLiquidity - 1000;

        fixture.verifyAddress(expectTester);
    }

    function testDepletedAssetPayoffDebtYWithReward() public {
        // move from (4, 3) to (6 + fee, 2.1) where y is depleted
        (, uint256 swapAmountYOut) = fixture.moveReservesRightToYValue(tester, 2.1e18);

        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 fromReserveY = 0.1e18;
        uint256 repayY = missingYAssets - fromReserveY;

        fixture.transferTokensTo(tester, 0, repayY);

        fixture.repayFor(tester, 0, repayY, 0, fromReserveY);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets,
                tokenY: reserveYBefore - missingYAssets + repayY,
                reserveXAssets: reserveXBefore,
                reserveYAssets: reserveYBefore - fromReserveY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceX = missingXAssets;
        expectTester.balanceY = missingYAssets + swapAmountYOut; // borrowed Y + swapAmountYOut + repayY - repayY
        expectTester.pairBorrowedX = missingXAssets;
        expectTester.pairBorrowedY = 0; // missingYAssets - (repayY + fromReserveY);
        expectTester.pairLiquidity = initialLiquidity - 1000;

        fixture.verifyAddress(expectTester);
    }

    function testDepletedAssetOverRepayYReplenishesFail() public {
        // move from (4, 3) to (6 + fee, 2.1) where y is depleted
        fixture.moveReservesRightToYValue(tester, 2.1e18);

        uint256 fromReserveY = 0.1e18;
        uint256 repayY = missingYAssets - fromReserveY + 1;

        fixture.transferTokensTo(tester, 0, repayY);

        vm.expectRevert(
            abi.encodeWithSelector(ERC20InsufficientBalance.selector, tester, missingYAssets, missingYAssets + 1)
        );
        fixture.repayForNoEvent(tester, 0, repayY);
    }

    function testDepletedAssetRepayYPartiallyReplenishes() public {
        // move from (4, 3) to (12 + fee, 2.05) where y is depleted
        (, uint256 swapAmountYOut) = fixture.moveReservesRightToYValue(tester, 2.05e18);

        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 repayY = 0.5e18;
        uint256 fromReserveY = repayY;

        fixture.transferTokensTo(tester, 0, repayY);
        // repay 0.5 Y to replenish moves curve to (8 + fee, 1.5), repayer get reduced 0.5 extra from debt
        fixture.repayFor(tester, 0, repayY, 0, fromReserveY);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets,
                tokenY: reserveYBefore - missingYAssets + repayY,
                reserveXAssets: reserveXBefore,
                reserveYAssets: reserveYBefore - fromReserveY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceX = missingXAssets;
        expectTester.balanceY = missingYAssets + swapAmountYOut; // borrowed Y + swapAmountYOut + repayY - repayY
        expectTester.pairBorrowedX = missingXAssets;
        expectTester.pairBorrowedY = missingYAssets - repayY - fromReserveY;
        expectTester.pairLiquidity = initialLiquidity - 1000;

        fixture.verifyAddress(expectTester);
    }
}
