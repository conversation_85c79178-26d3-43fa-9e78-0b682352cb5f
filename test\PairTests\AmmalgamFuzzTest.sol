// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';

import {FactoryPairTestFixture, MAX_TOKEN, MAXIMUM_AMM_RESERVE} from 'test/shared/FactoryPairTestFixture.sol';
import {mapSeedToRange, MINIMUM_LIQUIDITY} from 'test/shared/utilities.sol';
import {DepletedAssetUtils} from 'test/utils/DepletedAssetUtils.sol';

contract AmmalgamFuzzTest is Test {
    address private tester;
    FactoryPairTestFixture private fixture;

    function setUp() public {
        tester = vm.addr(123);
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
    }

    function testSwapFuzzTestCase(uint112 swapVal, uint112 tokenXVal, uint112 tokenYVal) public {
        (uint256 swapAmount, uint256 tokenXAmount, uint256 tokenYAmount) =
            generateSwapFuzzCase(swapVal, tokenXVal, tokenYVal);
        fixture.transferTokensTo(tester, swapAmount, 0);
        fixture.runSwapTestCase(tester, swapAmount, tokenXAmount, tokenYAmount);
    }

    function testDepositSwapFuzzTestCase(
        uint112 depositX,
        uint112 depositY,
        uint112 swapVal,
        uint112 tokenXVal,
        uint112 tokenYVal
    ) public {
        (uint256 swapAmount, uint256 tokenXAmount, uint256 tokenYAmount) =
            generateSwapFuzzCase(swapVal, tokenXVal, tokenYVal);
        uint256 _depositX = uint256(depositX);
        uint256 _depositY = uint256(depositY);
        fixture.transferTokensTo(tester, tokenXAmount + _depositX + swapAmount, tokenYAmount + _depositY);
        fixture.mintFor(tester, tokenXAmount, tokenYAmount);
        fixture.depositFor(tester, _depositX, _depositY);

        fixture.verifySwapXToY(tester, swapAmount, tokenXAmount, tokenYAmount);
    }

    /**
     * @notice The random seeds are mapped to acceptable bounds for the testSwapFuzzCase tests. Calculations
     * for these bounds are numbered with references that can be found at:
     * https://duelinggalois.notion.site/Swap-test-case-fuzz-limits-9dc0e31ea47745879cdcca116275f28e
     */
    function generateSwapFuzzCase(
        uint256 swapSeed,
        uint256 tokenXAmountSeed,
        uint256 tokenYAmountSeed
    ) private pure returns (uint256 _swapAmount, uint256 _tokenXAmount, uint256 _tokenYAmount) {
        // See (8) in @notice link, there must always be one unit in the reserves and enough room for a swap
        // of one without an overflow.
        _tokenXAmount = mapSeedToRange(tokenXAmountSeed, 1, MAXIMUM_AMM_RESERVE - 2);

        // See (19)
        // Must be at least 2 to allow for reserves after removing a swap of unit 1,
        // Must meet the minimum mint liquidity,
        // Must be large enough to ensure any swap size will not overflow reserveX.
        uint256 minTokenYAmount = Math.max(
            2,
            Math.max(
                Math.ceilDiv((MINIMUM_LIQUIDITY + 1) ** 2, _tokenXAmount),
                Math.ceilDiv(1000 * _tokenXAmount, 997 * (MAXIMUM_AMM_RESERVE - _tokenXAmount)) + 1
            )
        );
        _tokenYAmount = mapSeedToRange(tokenYAmountSeed, minTokenYAmount, MAXIMUM_AMM_RESERVE);

        // See (17)
        // The swap amount must be at least 1 and also large enough that the output is also at least 1.
        uint256 minSwapAmount = Math.max(1, Math.ceilDiv(1000 * _tokenXAmount, (997 * (_tokenYAmount - 1))));
        // The swap amount must not overflow the reserve veraible when added to it during the swap.
        uint256 maxSwapAmount = MAXIMUM_AMM_RESERVE - _tokenXAmount;
        _swapAmount = mapSeedToRange(swapSeed, minSwapAmount, maxSwapAmount);
    }
}
