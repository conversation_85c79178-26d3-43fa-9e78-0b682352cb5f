// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {IERC20Metadata} from '@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol';
import {
    BOR<PERSON>W_L,
    BORROW_X,
    BORROW_Y,
    DEPOSIT_L,
    DEPOSIT_X,
    DEPOSIT_Y
} from 'contracts/interfaces/tokens/ITokenController.sol';

import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';

contract ERC20MetaDataTests is Test {
    FactoryPairTestFixture private fixture;

    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
    }

    function testNameAndSymbolLiquidity() public view {
        IERC20Metadata metaData = IERC20Metadata(fixture.pair().tokens(DEPOSIT_L));
        assertEq(metaData.name(), 'Ammalgam Liquidity STUBX-STUBY');
        assertEq(metaData.symbol(), 'AMG-STUBX-STUBY');
    }

    function testTokenNameAndSymbolDepositX() public view {
        IERC20Metadata metaData = IERC20Metadata(fixture.pair().tokens(DEPOSIT_X));
        assertEq(metaData.name(), 'Ammalgam Deposited STUBX');
        assertEq(metaData.symbol(), 'AMG-STUBX');
    }

    function testTokenNameAndSymbolDepositY() public view {
        IERC20Metadata metaData = IERC20Metadata(fixture.pair().tokens(DEPOSIT_Y));
        assertEq(metaData.name(), 'Ammalgam Deposited STUBY');
        assertEq(metaData.symbol(), 'AMG-STUBY');
    }

    function testTokenNameAndSymbolBorrowedX() public view {
        IERC20Metadata metaData = IERC20Metadata(fixture.pair().tokens(BORROW_X));
        assertEq(metaData.name(), 'Ammalgam Borrowed STUBX');
        assertEq(metaData.symbol(), 'AMGB-STUBX');
    }

    function testTokenNameAndSymbolBorrowedY() public view {
        IERC20Metadata metaData = IERC20Metadata(fixture.pair().tokens(BORROW_Y));
        assertEq(metaData.name(), 'Ammalgam Borrowed STUBY');
        assertEq(metaData.symbol(), 'AMGB-STUBY');
    }

    function testTokenNameAndSymbolBorrowedLiquidity() public view {
        IERC20Metadata metaData = IERC20Metadata(fixture.pair().tokens(BORROW_L));
        assertEq(metaData.name(), 'Ammalgam Borrowed Liquidity STUBX-STUBY');
        assertEq(metaData.symbol(), 'AMGB-STUBX-STUBY');
    }
}
