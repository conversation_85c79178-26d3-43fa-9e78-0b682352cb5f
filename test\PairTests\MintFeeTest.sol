// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {computeExpectedSwapOutAmount} from 'test/shared/utilities.sol';

contract MintFeeTest is Test {
    IAmmalgamFactory private factory;
    IAmmalgamPair private pair;
    address private pairAddress;

    address private tester;
    address private tester2;

    FactoryPairTestFixture private fixture;

    mapping(address => uint256) private initialBalanceX;
    mapping(address => uint256) private initialBalanceY;

    struct MintFeeVars {
        uint256 tokenXAmount;
        uint256 tokenYAmount;
        uint256 firstMintL;
        uint256 swapAmount;
        uint256 amountOut;
        uint256 secondMintL;
        uint256 totalBalanceX;
        uint256 totalBalanceY;
        uint256 totalSupply;
        uint256 burnX;
        uint256 burnY;
    }

    function setUp() public {
        tester = vm.addr(1111);
        tester2 = vm.addr(1112);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        factory = fixture.factory();
        pair = fixture.pair();
        pairAddress = address(pair);

        initialBalanceX[tester] = 5000e18;
        initialBalanceY[tester] = 5000e18;
        initialBalanceX[tester2] = 5000e18;
        initialBalanceY[tester2] = 5000e18;

        fixture.transferTokensTo(tester, initialBalanceX[tester], initialBalanceX[tester]).transferTokensTo(
            tester2, initialBalanceX[tester2], initialBalanceX[tester2]
        );
    }
}
