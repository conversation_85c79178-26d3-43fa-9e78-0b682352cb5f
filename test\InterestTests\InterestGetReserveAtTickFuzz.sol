// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';
import {Interest} from 'contracts/libraries/Interest.sol';
import {Q128} from 'contracts/libraries/constants.sol';

contract InterestGetReserveAtTickCantExceedUin112MaxFuzz is Test {
    uint256 constant MAX_UINT112 = type(uint112).max;

    function testGetReserveAtTickFuzz(uint112 reserveX, uint112 reserveY) public pure {
        reserveX = uint112(bound(reserveX, 1, MAX_UINT112));
        reserveY = uint112(bound(reserveY, 1, MAX_UINT112));

        uint256 activeLiquidityAssets = Math.sqrt(uint256(reserveX) * reserveY);

        uint256 _priceX96 = Math.mulDiv(reserveX, Q128, reserveY);
        int16 tick = TickMath.getTickAtPrice(_priceX96);
        (uint256 reserveXAfter, uint256 reserveYAfter) = Interest.getReservesAtTick(activeLiquidityAssets, tick);

        assertLe(reserveXAfter, MAX_UINT112, 'reserveX should not exceed MAX_UINT112');
        assertLe(reserveYAfter, MAX_UINT112, 'reserveY should not exceed MAX_UINT112');
    }
}
