// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {MAG2, MAG2, Q72, Q144, TRANCHE_B_IN_Q72, TRANCHE_QUARTER_B_IN_Q72} from 'contracts/libraries/constants.sol';

import {Saturation} from 'contracts/libraries/Saturation.sol';

// used to create test values assuming liq start from 60% == (1+0.6)/0.6 << 72 == 160/60 << 72
uint256 constant ONE_PLUS_START_LIQ_OVER_START_LIQ_Q72 = 0x2aaaaaaaaaaaaaaaaaa;

// a value close to liquidation.
uint256 constant CLOSE_TO_START_OF_LIQUIDATION_MAG2 = 70;

uint256 constant TICKS_PER_QUARTER_TRANCHE = 25; // 25 ticks per quarter tranche

/**
 * @dev create a series of borrows to fit into each tranche and sum them together
 *   this is a sum of series that telescopic series due to
 *   $$\sum_{i=0}^{x-1} B^{i} = \frac{(B^x - 1)}{B-1} $$
 *   ```math
 *     \frac{L\cdot D_{esiredThreshold}}{S_{atBuffer}}B^{t_{0}}\left(1-\frac{1}{B^{x}}\right)
 *   ```
 */
function sumOfSeriesOfBorrowX(
    uint256 sqrtPriceQ72,
    uint256 trancheSpanInTicks,
    uint256 _activeLiquidityAssets,
    uint256 desiredThresholdMag2
) pure returns (uint256 borrowX) {
    uint256 bToThePowerOfCountQ72 = Q72;

    for (uint256 i = 0; i < trancheSpanInTicks / TICKS_PER_QUARTER_TRANCHE; i++) {
        bToThePowerOfCountQ72 = bToThePowerOfCountQ72 * TRANCHE_QUARTER_B_IN_Q72 / Q72;
    }

    borrowX = Math.mulDiv(
        _activeLiquidityAssets * desiredThresholdMag2,
        sqrtPriceQ72 * (bToThePowerOfCountQ72 - Q72),
        Q72 * bToThePowerOfCountQ72 * Saturation.SATURATION_TIME_BUFFER_IN_MAG2,
        Math.Rounding.Ceil
    );
}

function sumOfSeriesOfBorrowY(
    uint256 sqrtPriceQ72,
    uint256 trancheSpanInTicks,
    uint256 _activeLiquidityAssets,
    uint256 desiredThresholdMag2
) pure returns (uint256 borrowY) {
    uint256 bToThePowerOfCountQ72 = Q72;

    for (uint256 i = 0; i < trancheSpanInTicks / TICKS_PER_QUARTER_TRANCHE; i++) {
        bToThePowerOfCountQ72 = bToThePowerOfCountQ72 * TRANCHE_QUARTER_B_IN_Q72 / Q72;
    }

    borrowY = Math.mulDiv(
        _activeLiquidityAssets * desiredThresholdMag2,
        (bToThePowerOfCountQ72 - Q72) * Q72,
        Saturation.SATURATION_TIME_BUFFER_IN_MAG2 * sqrtPriceQ72 * bToThePowerOfCountQ72
    );
}

/**
 * @dev formula for the series of collateral Y that is needed to cover the borrow X.
 * math```
 *   \frac{L\cdot\left(B^{c_{ount}}-1\right)}{C_{LTV}\cdot B^{t_{0}}\left(B\left(\frac{S_{atBuffer}}{D_{esiredThreshold}}-1\right)+1\right)}
 * ```
 */
function sumOfSeriesOfCollateralY(
    uint256 sqrtPriceQ72,
    uint256 trancheSpanInTicks,
    uint256 _activeLiquidityAssets,
    uint256 desiredThresholdMag2
) pure returns (uint256 collateralY) {
    uint256 bToThePowerOfCountQ72 = Q72;

    for (uint256 i = 0; i < trancheSpanInTicks / TICKS_PER_QUARTER_TRANCHE; i++) {
        bToThePowerOfCountQ72 = bToThePowerOfCountQ72 * TRANCHE_QUARTER_B_IN_Q72 / Q72;
    }

    uint256 denominatorQ72Mag4 =
        Saturation.SATURATION_TIME_BUFFER_IN_MAG2 * Saturation.EXPECTED_SATURATION_LTV_MAG2 * sqrtPriceQ72;

    collateralY = Math.mulDiv(
        _activeLiquidityAssets, desiredThresholdMag2 * (bToThePowerOfCountQ72 - Q72) * MAG2, denominatorQ72Mag4
    );
}

function sumOfSeriesOfCollateralX(
    uint256 sqrtPriceQ72,
    uint256 trancheSpanInTicks,
    uint256 _activeLiquidityAssets,
    uint256 desiredThresholdMag2
) pure returns (uint256 collateralX) {
    uint256 bToThePowerOfCountQ72 = Q72;

    for (uint256 i = 0; i < trancheSpanInTicks / TICKS_PER_QUARTER_TRANCHE; i++) {
        bToThePowerOfCountQ72 = bToThePowerOfCountQ72 * TRANCHE_QUARTER_B_IN_Q72 / Q72;
    }

    collateralX = Math.mulDiv(
        _activeLiquidityAssets * sqrtPriceQ72,
        desiredThresholdMag2 * (bToThePowerOfCountQ72 - Q72) * MAG2,
        Saturation.SATURATION_TIME_BUFFER_IN_MAG2 * Saturation.EXPECTED_SATURATION_LTV_MAG2 * Q144
    );
}

function sumOfSeriesOfCollateralL(
    uint256 sqrtPriceQ72,
    uint256 trancheSpanInTicks,
    uint256 _activeLiquidityAssets,
    uint256 desiredThresholdMag2,
    bool againstBorrowedX
) pure returns (uint256 collateralL) {
    require(trancheSpanInTicks == 100, 'each tranche has a different price, so we need to use a series');
    if (againstBorrowedX) {
        uint256 collateralY =
            sumOfSeriesOfCollateralY(sqrtPriceQ72, trancheSpanInTicks, _activeLiquidityAssets, desiredThresholdMag2);

        // convert to L using $$y \cdot \frac{sqrtPrice0 \cdot B}{B - 1}$$, LTV is 85% at expected
        // loan to value, we want to use 1.85%
        collateralL = collateralY * sqrtPriceQ72 / Q72;
    } else {
        uint256 collateralX =
            sumOfSeriesOfCollateralX(sqrtPriceQ72, trancheSpanInTicks, _activeLiquidityAssets, desiredThresholdMag2);

        // convert to L using $$x \cdot \frac{B}{B - 1}$$, LTV is 85% at expected
        // loan to value, we want to use 1.85%
        collateralL = collateralX * Q72 / sqrtPriceQ72;
    }
}

function sumOfSeriesOfBorrowL(
    uint256 sqrtPriceQ72,
    uint256 trancheSpanInTicks,
    uint256 _activeLiquidityAssets,
    uint256 desiredThresholdMag2
) pure returns (uint256 borrowL) {
    require(trancheSpanInTicks == 100, 'each tranche has a different price, so we need to use a series');
    uint256 borrowX =
        sumOfSeriesOfBorrowX(sqrtPriceQ72, trancheSpanInTicks, _activeLiquidityAssets, desiredThresholdMag2);

    borrowL = borrowX * Q72 / sqrtPriceQ72;
}

// create LXY shares

// LDLB
// XDLB x
// LDXDLB
// YDLB x
// LDYDLB
// XDYDLB x
// LDXDYDLB
// LDXB x
// YDXB x
// LDYDXB x
// LDLBXB
// YDLBXB x
// LDYDLBXB
// LDYB x
// XDYB x
// LDXDYB x
// LDLBYB
// XDLBYB x
// LDXDLBYB
// LDXBYB x
// LDLBXBYB

struct LXY {
    uint256 LD;
    uint256 XD;
    uint256 YD;
    uint256 LB;
    uint256 XB;
    uint256 YB;
}

function createLXYAssetsXDLB(
    uint256 targetSaturationInLAssets,
    uint256 targetLiqSqrtPriceXInQ72
) pure returns (LXY memory accountLXYShares) {
    targetSaturationInLAssets = adjustForBufferAndBase(targetSaturationInLAssets);

    uint256 XD = Math.mulDiv(
        Math.mulDiv(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72, Q72),
        ONE_PLUS_START_LIQ_OVER_START_LIQ_Q72,
        Q72
    );
    uint256 LB = targetSaturationInLAssets;
    require(0 < XD && 0 < LB);
    accountLXYShares = LXY({LD: 0, XD: XD, YD: 0, LB: LB, XB: 0, YB: 0});
}

function createLXYAssetsYDLB(
    uint256 targetSaturationInLAssets,
    uint256 targetLiqSqrtPriceXInQ72
) pure returns (LXY memory accountLXYShares) {
    targetSaturationInLAssets = adjustForBufferAndBase(targetSaturationInLAssets);

    uint256 YD = Math.mulDiv(
        Math.mulDiv(targetSaturationInLAssets, Q72, targetLiqSqrtPriceXInQ72),
        ONE_PLUS_START_LIQ_OVER_START_LIQ_Q72,
        Q72
    );
    uint256 LB = targetSaturationInLAssets;
    require(0 < YD && 0 < LB);
    accountLXYShares = LXY({LD: 0, XD: 0, YD: YD, LB: LB, XB: 0, YB: 0});
}

function createLXYAssetsXDYDLBNetX(
    uint256 targetSaturationInLAssets,
    uint256 targetNetXLiqSqrtPriceXInQ72,
    int256 ratioYHatOverXHatInQ72
) pure returns (LXY memory accountLXYShares) {
    targetSaturationInLAssets = adjustForBufferAndBase(targetSaturationInLAssets);

    require(int256(Q72) < calcRatioTimesPriceXInQ72(ratioYHatOverXHatInQ72, targetNetXLiqSqrtPriceXInQ72));

    bool netX = true;
    (int256 LHat, int256 XHat, int256 YHat) =
        createLXYHats(targetSaturationInLAssets, targetNetXLiqSqrtPriceXInQ72, ratioYHatOverXHatInQ72, netX);

    require(XHat < 0);
    require(YHat < 0);
    require(0 < LHat);

    uint256 XD = uint256(-XHat);
    uint256 YD = uint256(-YHat);
    uint256 LB = uint256(LHat);
    accountLXYShares = LXY({LD: 0, XD: XD, YD: YD, LB: LB, XB: 0, YB: 0});
}

function createLXYAssetsXDYDLBNetY(
    uint256 targetSaturationInLAssets,
    uint256 targetNetXLiqSqrtPriceXInQ72,
    int256 ratioYHatOverXHatInQ72
) pure returns (LXY memory accountLXYShares) {
    targetSaturationInLAssets = adjustForBufferAndBase(targetSaturationInLAssets);

    bool netX = false;
    require(int256(Q72) < calcRatioTimesPriceXInQ72(ratioYHatOverXHatInQ72, targetNetXLiqSqrtPriceXInQ72));
    (int256 LHat, int256 XHat, int256 YHat) =
        createLXYHats(targetSaturationInLAssets, targetNetXLiqSqrtPriceXInQ72, ratioYHatOverXHatInQ72, netX);
    require(XHat < 0);
    require(YHat < 0);
    require(0 < LHat);

    uint256 XD = uint256(-XHat);
    uint256 YD = uint256(-YHat);
    uint256 LB = uint256(LHat);
    accountLXYShares = LXY({LD: 0, XD: XD, YD: YD, LB: LB, XB: 0, YB: 0});
}

function createLXYAssetsLDXB(
    uint256 targetSaturationInLAssets,
    uint256 targetLiqSqrtPriceXInQ72
) pure returns (LXY memory accountLXYShares) {
    targetSaturationInLAssets = adjustForBufferAndBase(targetSaturationInLAssets);

    uint256 XB = Math.mulDiv(
        Math.mulDiv(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72, Q72),
        ONE_PLUS_START_LIQ_OVER_START_LIQ_Q72,
        Q72
    );
    uint256 LD = (MAG2 * targetSaturationInLAssets) / CLOSE_TO_START_OF_LIQUIDATION_MAG2;
    require(0 < XB && 0 < LD);
    accountLXYShares = LXY({LD: LD, XD: 0, YD: 0, LB: 0, XB: XB, YB: 0});
}

function createLXYAssetsYDXB(
    uint256 targetSaturationInLAssets,
    uint256 targetLiqSqrtPriceXInQ72
) pure returns (LXY memory accountLXYShares) {
    targetSaturationInLAssets = adjustForBufferAndBase(targetSaturationInLAssets);

    uint256 XB = Math.mulDiv(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72, Q72);
    uint256 YD = Math.mulDiv(
        targetSaturationInLAssets * MAG2, Q72, targetLiqSqrtPriceXInQ72 * CLOSE_TO_START_OF_LIQUIDATION_MAG2
    );
    require(0 < XB && 0 < YD);
    accountLXYShares = LXY({LD: 0, XD: 0, YD: YD, LB: 0, XB: XB, YB: 0});
}

function createLXYAssetsLDYDXB(
    uint256 targetSaturationInLAssets,
    uint256 targetLiqSqrtPriceXInQ72,
    int256 ratioYHatOverXHatInQ72
) pure returns (LXY memory accountLXYShares) {
    targetSaturationInLAssets = adjustForBufferAndBase(targetSaturationInLAssets);

    require(ratioYHatOverXHatInQ72 < 0);

    bool netX = true;
    (int256 LHat, int256 XHat, int256 YHat) =
        createLXYHats(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72, ratioYHatOverXHatInQ72, netX);

    require(0 < XHat);
    require(YHat < 0);
    require(LHat < 0);

    uint256 XB = uint256(XHat);
    uint256 LD = uint256(-LHat);
    uint256 YD = uint256(-YHat);
    accountLXYShares = LXY({LD: LD, XD: 0, YD: YD, LB: 0, XB: XB, YB: 0});
}

function createLXYAssetsYDLBXB(
    uint256 targetSaturationInLAssets,
    uint256 targetLiqSqrtPriceXInQ72,
    int256 ratioYHatOverXHatInQ72
) pure returns (LXY memory accountLXYShares) {
    targetSaturationInLAssets = adjustForBufferAndBase(targetSaturationInLAssets);

    require(ratioYHatOverXHatInQ72 < 0, 'Yhat < 0');

    bool netX = true;
    (int256 LHat, int256 XHat, int256 YHat) =
        createLXYHats(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72, ratioYHatOverXHatInQ72, netX);

    require(0 < LHat, 'LHat > 0');
    require(0 < XHat, 'XHat > 0');
    require(YHat < 0, 'YHat < 0');

    uint256 LB = uint256(LHat);
    uint256 XB = uint256(XHat);
    uint256 YD = uint256(-YHat);

    accountLXYShares = LXY({LD: 0, XD: 0, YD: YD, LB: LB, XB: XB, YB: 0});
}

function createLXYAssetsLDYB(
    uint256 targetSaturationInLAssets,
    uint256 targetLiqSqrtPriceXInQ72
) pure returns (LXY memory accountLXYShares) {
    targetSaturationInLAssets = adjustForBufferAndBase(targetSaturationInLAssets);

    uint256 YB = Math.mulDiv(
        Math.mulDiv(targetSaturationInLAssets, Q72, targetLiqSqrtPriceXInQ72),
        ONE_PLUS_START_LIQ_OVER_START_LIQ_Q72,
        Q72
    );
    uint256 LD = (MAG2 * targetSaturationInLAssets) / CLOSE_TO_START_OF_LIQUIDATION_MAG2;
    require(0 < YB && 0 < LD);
    accountLXYShares = LXY({LD: LD, XD: 0, YD: 0, LB: 0, XB: 0, YB: YB});
}

function createLXYAssetsXDYB(
    uint256 targetSaturationInLAssets,
    uint256 targetLiqSqrtPriceXInQ72
) pure returns (LXY memory accountLXYShares) {
    targetSaturationInLAssets = adjustForBufferAndBase(targetSaturationInLAssets);

    uint256 XD = (Math.mulDiv(MAG2 * targetSaturationInLAssets, targetLiqSqrtPriceXInQ72, Q72))
        / CLOSE_TO_START_OF_LIQUIDATION_MAG2;
    uint256 YB = Math.mulDiv(targetSaturationInLAssets, Q72, targetLiqSqrtPriceXInQ72);
    require(0 < XD && 0 < YB);
    accountLXYShares = LXY({LD: 0, XD: XD, YD: 0, LB: 0, XB: 0, YB: YB});
}

function createLXYAssetsLDXDYB(
    uint256 targetSaturationInLAssets,
    uint256 targetLiqSqrtPriceXInQ72,
    int256 ratioYHatOverXHatInQ72
) pure returns (LXY memory accountLXYShares) {
    targetSaturationInLAssets = adjustForBufferAndBase(targetSaturationInLAssets);

    bool netX = false;
    require(ratioYHatOverXHatInQ72 < 0);
    (int256 LHat, int256 XHat, int256 YHat) =
        createLXYHats(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72, ratioYHatOverXHatInQ72, netX);
    require(XHat < 0);
    require(0 < YHat);
    require(LHat < 0);

    uint256 YB = uint256(YHat);
    uint256 LD = uint256(-LHat);
    uint256 XD = uint256(-XHat);
    accountLXYShares = LXY({LD: LD, XD: XD, YD: 0, LB: 0, XB: 0, YB: YB});
}

function createLXYAssetsXDLBYB(
    uint256 targetSaturationInLAssets,
    uint256 targetLiqSqrtPriceXInQ72,
    int256 ratioYHatOverXHatInQ72
) pure returns (LXY memory accountLXYShares) {
    targetSaturationInLAssets = adjustForBufferAndBase(targetSaturationInLAssets);

    bool netX = false;
    require(ratioYHatOverXHatInQ72 < 0);
    (int256 LHat, int256 XHat, int256 YHat) =
        createLXYHats(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72, ratioYHatOverXHatInQ72, netX);
    require(XHat < 0);
    require(0 < YHat);
    require(0 < LHat);

    uint256 LB = uint256(LHat);
    uint256 YB = uint256(YHat);
    uint256 XD = uint256(-XHat);
    accountLXYShares = LXY({LD: 0, XD: XD, YD: 0, LB: LB, XB: 0, YB: YB});
}

function createLXYAssetsLDXBYBNetX(
    uint256 targetSaturationInLAssets,
    uint256 targetNetXLiqSqrtPriceXInQ72,
    int256 ratioYHatOverXHatInQ72
) pure returns (LXY memory accountLXYShares) {
    targetSaturationInLAssets = adjustForBufferAndBase(targetSaturationInLAssets);

    require(calcRatioTimesPriceXInQ72(ratioYHatOverXHatInQ72, targetNetXLiqSqrtPriceXInQ72) < int256(Q72));

    bool netX = true;
    (int256 LHat, int256 XHat, int256 YHat) =
        createLXYHats(targetSaturationInLAssets, targetNetXLiqSqrtPriceXInQ72, ratioYHatOverXHatInQ72, netX);

    require(0 < XHat);
    require(0 < YHat);
    require(LHat < 0);

    uint256 XB = uint256(XHat);
    uint256 YB = uint256(YHat);
    uint256 LD = uint256(-LHat);
    accountLXYShares = LXY({LD: LD, XD: 0, YD: 0, LB: 0, XB: XB, YB: YB});
}

function createLXYAssetsLDXBYBNetY(
    uint256 targetSaturationInLAssets,
    uint256 targetNetXLiqSqrtPriceXInQ72,
    int256 ratioYHatOverXHatInQ72
) pure returns (LXY memory accountLXYShares) {
    targetSaturationInLAssets = adjustForBufferAndBase(targetSaturationInLAssets);

    bool netX = false;
    require(calcRatioTimesPriceXInQ72(ratioYHatOverXHatInQ72, targetNetXLiqSqrtPriceXInQ72) < int256(Q72));
    (int256 LHat, int256 XHat, int256 YHat) =
        createLXYHats(targetSaturationInLAssets, targetNetXLiqSqrtPriceXInQ72, ratioYHatOverXHatInQ72, netX);
    require(0 < XHat);
    require(0 < YHat);
    require(LHat < 0);

    uint256 XB = uint256(XHat);
    uint256 YB = uint256(YHat);
    uint256 LD = uint256(-LHat);
    accountLXYShares = LXY({LD: LD, XD: 0, YD: 0, LB: 0, XB: XB, YB: YB});
}

function adjustForBufferAndBase(
    uint256 targetSaturationInLAssets
) pure returns (uint256 adjustedTargetSat) {
    adjustedTargetSat = Math.mulDiv(
        targetSaturationInLAssets,
        MAG2 * Q72,
        Saturation.SATURATION_TIME_BUFFER_IN_MAG2 * Saturation.TRANCHE_BASE_OVER_BASE_MINUS_ONE_Q72
    );
}

// create LXY helpers

// a * b / c
function mulDivInt(int256 a, int256 b, int256 c) pure returns (int256) {
    bool aPositive = 0 <= a;
    bool bPositive = 0 <= b;
    bool cPositive = 0 <= c;
    uint256 aUint = uint256(a);
    uint256 aNegUint = uint256(-a);
    uint256 bUint = uint256(b);
    uint256 bNegUint = uint256(-b);
    uint256 cUint = uint256(c);
    uint256 cNegUint = uint256(-c);
    if (aPositive && bPositive && cPositive) {
        return int256(Math.mulDiv(aUint, bUint, cUint));
    } else if (aPositive && bPositive && !cPositive) {
        return -int256(Math.mulDiv(aUint, bUint, cNegUint));
    } else if (aPositive && !bPositive && cPositive) {
        return -int256(Math.mulDiv(aUint, bNegUint, cUint));
    } else if (aPositive && !bPositive && !cPositive) {
        return int256(Math.mulDiv(aUint, bNegUint, cNegUint));
    } else if (!aPositive && bPositive && cPositive) {
        return -int256(Math.mulDiv(aNegUint, bUint, cUint));
    } else if (!aPositive && bPositive && !cPositive) {
        return int256(Math.mulDiv(aNegUint, bUint, cNegUint));
    } else if (!aPositive && !bPositive && cPositive) {
        return int256(Math.mulDiv(aNegUint, bNegUint, cUint));
    } else if (!aPositive && !bPositive && !cPositive) {
        return -int256(Math.mulDiv(aNegUint, bNegUint, cNegUint));
    } else {
        revert('impossible');
    }
}

function calcRatioTimesPriceXInQ72(
    int256 ratioYHatOverXHatInQ72,
    uint256 targetLiqSqrtPriceXInQ72
) pure returns (int256 ratioTimesPriceXInQ72) {
    ratioTimesPriceXInQ72 = int256(Math.mulDiv(targetLiqSqrtPriceXInQ72, targetLiqSqrtPriceXInQ72, Q72));
    ratioTimesPriceXInQ72 = mulDivInt(ratioTimesPriceXInQ72, ratioYHatOverXHatInQ72, int256(Q72));
}

function calcXHat(
    uint256 targetSaturationInLAssets, // σ
    uint256 targetLiqSqrtPriceXInQ72, // rX
    int256 ratioYHatOverXHatInQ72, // α
    bool isNetX
) pure returns (int256 XHat) {
    if (ratioYHatOverXHatInQ72 == type(int200).max) return XHat; // inf <=> X̂ ≡ 0

    int256 XHatDenomInQ72 = calcRatioTimesPriceXInQ72(ratioYHatOverXHatInQ72, targetLiqSqrtPriceXInQ72);
    XHatDenomInQ72 = int256(Q72) - XHatDenomInQ72;
    // XHatDenomInQ72 == LTVMAX * (1 - α * rX^2)

    XHat = int256(
        Math.mulDiv(
            targetSaturationInLAssets * Saturation.EXPECTED_SATURATION_LTV_PLUS_ONE_MAG2, targetLiqSqrtPriceXInQ72, MAG2
        )
    );
    XHat = mulDivInt(XHat, int256(MAG2), int256(75) * XHatDenomInQ72);
    // XHat == 2 * σ * rX * (1+LTVMAX) / (LTVMAX * (1 - α * rX^2))

    if (!isNetX) XHat *= -1;
}

function calcYHat(
    uint256 targetSaturationInLAssets, // σ
    uint256 targetLiqSqrtPriceXInQ72, // rX
    int256 ratioYHatOverXHatInQ72, // α
    bool isNetX,
    int256 XHat
) pure returns (int256 YHat) {
    if (ratioYHatOverXHatInQ72 < type(int200).max) {
        return mulDivInt(ratioYHatOverXHatInQ72, XHat, int256(Q72));
    } // Ŷ = α * X̂

    // isinf(α) <=> X̂ ≡ 0

    YHat = int256(Math.mulDiv(targetSaturationInLAssets, Saturation.EXPECTED_SATURATION_LTV_PLUS_ONE_MAG2, MAG2));
    YHat = int256(Math.mulDiv(uint256(YHat), MAG2 * Q72, 75 * targetLiqSqrtPriceXInQ72));

    if (isNetX) YHat *= -1;
}

function calcLHat(
    uint256 targetSaturationInLAssets,
    uint256 targetLiqSqrtPriceXInQ72,
    bool isNetX,
    int256 XHat,
    int256 YHat
) pure returns (int256 LHat) {
    LHat = int256(targetSaturationInLAssets)
        - (
            isNetX
                ? mulDivInt(int256(Q72), XHat, int256(targetLiqSqrtPriceXInQ72))
                : mulDivInt(YHat, int256(targetLiqSqrtPriceXInQ72), int256(Q72))
        );
}

function createLXYHats(
    uint256 targetSaturationInLAssets,
    uint256 targetLiqSqrtPriceXInQ72,
    int256 ratioYHatOverXHatInQ72,
    bool isNetX
) pure returns (int256 LHat, int256 XHat, int256 YHat) {
    XHat = calcXHat(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72, ratioYHatOverXHatInQ72, isNetX);
    YHat = calcYHat(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72, ratioYHatOverXHatInQ72, isNetX, XHat);
    LHat = calcLHat(targetSaturationInLAssets, targetLiqSqrtPriceXInQ72, isNetX, XHat, YHat);
}

function getSatPerTrancheOrZero(
    Saturation.SaturationPair[] memory arr,
    uint256 index
) pure returns (Saturation.SaturationPair memory) {
    if (index < arr.length) {
        return arr[index];
    } else {
        return Saturation.SaturationPair({satInLAssets: 0, satRelativeToL: 0});
    }
}
