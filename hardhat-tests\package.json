{"devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.9", "@nomicfoundation/hardhat-ethers": "^3.0.9", "@nomicfoundation/hardhat-ignition": "^0.15.12", "@nomicfoundation/hardhat-ignition-ethers": "^0.15.13", "@nomicfoundation/hardhat-network-helpers": "^1.0.13", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/hardhat-verify": "^2.0.14", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "@types/chai": "^4.3.20", "@types/dotenv": "^8.2.3", "@types/mocha": "^10.0.10", "@types/node": "^24.0.10", "chai": "^4.5.0", "ethers": "^6.15.0", "hardhat": "^2.25.0", "hardhat-gas-reporter": "^1.0.10", "solidity-coverage": "^0.8.16", "ts-node": "^10.9.2", "typechain": "^8.3.2", "typescript": "^5.8.3"}, "dependencies": {"@1inch/solidity-utils": "^6.6.0", "@1inch/token-plugins": "^1.3.0", "@mangrovedao/mangrove-core": "2.1.2-0", "@morpho-org/morpho-blue": "^1.0.0", "@openzeppelin/contracts": "^5.3.0", "dotenv": "^16.6.1"}, "pnpm": {"overrides": {"ws": ">=8.17.1", "axios": ">=0.30.0", "cookie": ">=0.7.0"}}}