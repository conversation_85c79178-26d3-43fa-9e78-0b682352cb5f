// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';
import {Validation} from 'contracts/libraries/Validation.sol';

import {FactoryPairTestFixture, IPairHarness, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {
    adjustAmountsByTickPrice,
    computeExpectedSwapInAmount,
    computeExpectedSwapInAmountWithoutFees,
    computeExpectedSwapOutAmount,
    computeExpectedSwapOutAmountWithoutFees
} from 'test/shared/utilities.sol';
import {Q72} from 'contracts/libraries/constants.sol';

contract ActiveLiquidityScalerTest is Test {
    IAmmalgamFactory private factory;
    IPairHarness private pair;

    address private pairAddress;
    address private random;
    address private tester;

    uint8 private constant LTV = 75;
    uint256 private constant initialX = 80e18;
    uint256 private constant initialY = 20e18;
    uint256 private constant initialLiquidity = 40e18;

    FactoryPairTestFixture private fixture;

    function setUp() public {
        random = vm.addr(1111);
        tester = vm.addr(111);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        factory = fixture.factory();
        pair = fixture.pair();
        pairAddress = address(pair);

        fixture.transferTokensTo(random, initialX, initialY);

        fixture.mintForAndInitializeBlocks(random, initialX, initialY);
    }

    function testScaledActiveLiquidityIsEqualToSqrtReserves() public {
        uint256 swapAmountYIn = 100e18;
        uint256 swapAmountXOut = computeExpectedSwapOutAmount(swapAmountYIn, initialY, initialX);

        fixture.transferTokensTo(pairAddress, 0, swapAmountYIn);
        pair.swap(swapAmountXOut, 0, tester, '');

        uint256 reserveXAfterSwap = initialX - swapAmountXOut;
        uint256 reserveYAfterSwap = initialY + swapAmountYIn;

        uint256 sqrtReserves = Math.sqrt(reserveXAfterSwap * reserveYAfterSwap);
        uint256 activeLiquidityAssets = fixture.activeLiquidityAssets();
        uint256 activeLiquidityScalerInQ72 = (sqrtReserves * Q72 / uint256(activeLiquidityAssets));

        assertLt(activeLiquidityAssets, sqrtReserves, 'activeLiquidityAssets < sqrtReserves');

        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        assertApproxEqAbs(activeLiquidityAssets * activeLiquidityScalerInQ72 / Q72, Math.sqrt(reserveX * reserveY), 1);
    }

    function testALS_MaxBorrowXAgainstLWithDifferentInitialReserves() public {
        // random user to mint into pool
        fixture.newBlock(1);
        fixture.transferTokensTo(random, 4e18, 1e18);
        fixture.mintFor(random, 4e18, 1e18);

        uint256 updatedReserveX = initialX + 4e18;
        uint256 updatedReserveY = initialY + 1e18;

        // tester mint into pool and used as collateral
        fixture.newBlock(1);
        uint256 mintX = 2e18;
        uint256 mintY = 0.5e18;
        fixture.transferTokensTo(tester, mintX, mintY);
        uint256 mintedL = fixture.mintFor(tester, mintX, mintY);

        updatedReserveX = updatedReserveX + mintX;
        updatedReserveY = updatedReserveY + mintY;

        // random user swap to affect the price
        {
            fixture.newBlock(1);
            uint256 swapAmountYIn = 2e18; // make a trade to generate different max tick
            uint256 swapAmountXOut = computeExpectedSwapOutAmount(swapAmountYIn, updatedReserveY, updatedReserveX);
            fixture.swapYInXOut(random, swapAmountYIn, swapAmountXOut);

            updatedReserveX = updatedReserveX - swapAmountXOut;
            updatedReserveY = updatedReserveY + swapAmountYIn;
        }

        // random user mint into pool again to affect activeLiquidityAssets
        {
            fixture.newBlock(1);
            uint256 newMintX = updatedReserveX * 2;
            uint256 newMintY = updatedReserveY * 2;
            fixture.transferTokensTo(random, newMintX, newMintY);
            fixture.mintFor(random, newMintX, newMintY);

            updatedReserveX = updatedReserveX + newMintX;
            updatedReserveY = updatedReserveY + newMintY;
        }

        fixture.newBlock(1);
        pair.sync();
        (int16 minTick, int16 maxTick) = pair.exposed_getTickRange(); // get minTick for negative X (borrow X)
        require(minTick != maxTick, 'Calling pair.sync() in a new block, the minTick and maxTick should not be equal');
        {
            // How much x and y does the tester have
            uint256 activeLiquidityAssets = fixture.activeLiquidityAssets();
            mintX = (mintedL * updatedReserveX) / activeLiquidityAssets;
            mintY = (mintedL * updatedReserveY) / activeLiquidityAssets;
        }

        // Slippage calculations for collateral liquidation excludes borrower's liquidity.
        uint256 reservesWithoutUsersLiquidityX = updatedReserveX - mintX;
        uint256 reservesWithoutUsersLiquidityY = updatedReserveY - mintY;

        (uint256 mintXAdj, uint256 mintYAdj) =
            adjustAmountsByTickPrice(mintX, mintY, updatedReserveX, updatedReserveY, minTick);
        (uint256 reservesXOut, uint256 reservesYIn) = adjustAmountsByTickPrice(
            reservesWithoutUsersLiquidityX, reservesWithoutUsersLiquidityY, updatedReserveX, updatedReserveY, minTick
        );

        uint256 excessBorrowedX =
            computeExpectedSwapOutAmountWithoutFees(mintYAdj * LTV / 100, reservesYIn, reservesXOut);
        uint256 borrowX = (mintXAdj + excessBorrowedX) - 1;

        // tester borrow
        verifyMaxBorrow(tester, borrowX, 0, 6);
    }

    function testALS_MaxBorrowXYAgainstLWithoutSyncSwap() public {
        uint256 mintX = 2e18;
        uint256 mintY = 0.5e18;
        uint256 borrowY = mintY / 2;
        uint256 borrowX;

        uint256 updatedReserveX = initialX;
        uint256 updatedReserveY = initialY;

        fixture.newBlock(1);
        fixture.transferTokensTo(tester, mintX, mintY);
        uint256 mintedL = fixture.mintFor(tester, mintX, mintY);

        updatedReserveX = updatedReserveX + mintX;
        updatedReserveY = updatedReserveY + mintY;

        {
            fixture.newBlock(1);
            uint256 swapAmountXIn = 12e18; // make a trade to generate different max tick
            uint256 swapAmountYOut = computeExpectedSwapOutAmount(swapAmountXIn, updatedReserveX, updatedReserveY);
            fixture.swapXInYOut(tester, swapAmountXIn, swapAmountYOut);

            updatedReserveX = updatedReserveX + swapAmountXIn;
            updatedReserveY = updatedReserveY - swapAmountYOut;
        }

        (int16 minTick, int16 maxTick) = pair.exposed_getTickRange(); // get minTick for negative X (borrow X)
        {
            // How much x and y does the tester have
            uint256 activeLiquidityAssets = fixture.activeLiquidityAssets();

            mintX = (mintedL * updatedReserveX) / activeLiquidityAssets;
            mintY = (mintedL * updatedReserveY) / activeLiquidityAssets;

            (uint256 mintXAdj, uint256 mintYAdj) =
                adjustAmountsByTickPrice(mintX, mintY, updatedReserveX, updatedReserveY, minTick, maxTick);
            (uint256 reservesXOut, uint256 reservesYIn) = adjustAmountsByTickPrice(
                updatedReserveX - mintX, updatedReserveY - mintY, updatedReserveX, updatedReserveY, minTick, maxTick
            );

            borrowX = mintXAdj
                + computeExpectedSwapOutAmountWithoutFees((mintYAdj - borrowY) * LTV / 100, reservesYIn, reservesXOut);
        }

        verifyMaxBorrow(tester, borrowX, borrowY, 4);
    }

    function testALS_MaxBorrowYAgainstL() public {
        uint256 mintX = 2e18;
        uint256 mintY = 0.5e18;

        fixture.newBlock(1);
        fixture.transferTokensTo(tester, mintX, mintY);
        uint256 mintedL = fixture.mintFor(tester, mintX, mintY);

        uint256 updatedReserveX = initialX + mintX;
        uint256 updatedReserveY = initialY + mintY;

        uint256 swapAmountYIn = 12e18;
        uint256 swapAmountXOut = computeExpectedSwapOutAmount(swapAmountYIn, initialY + mintY, initialX + mintX);

        fixture.newBlock(1);
        fixture.swapYInXOut(tester, swapAmountYIn, swapAmountXOut);

        updatedReserveX -= swapAmountXOut;
        updatedReserveY += swapAmountYIn;

        fixture.newBlock(1);
        pair.sync();
        (int16 minTick, int16 maxTick) = pair.exposed_getTickRange(); // get minTick for negative X (borrow X)
        require(minTick != maxTick, 'Calling pair.sync() in a new block, the minTick and maxTick should not be equal');
        {
            // How much x and y from mintL does the tester have now
            uint256 activeLiquidityAssets = fixture.activeLiquidityAssets();
            mintX = (mintedL * updatedReserveX) / activeLiquidityAssets;
            mintY = (mintedL * updatedReserveY) / activeLiquidityAssets;
        }
        // Slippage calculations for collateral liquidation excludes borrower's liquidity.
        uint256 reservesWithoutUsersLiquidityX = updatedReserveX - mintX;
        uint256 reservesWithoutUsersLiquidityY = updatedReserveY - mintY;

        (uint256 mintXAdj, uint256 mintYAdj) =
            adjustAmountsByTickPrice(mintX, mintY, updatedReserveX, updatedReserveY, maxTick);
        (uint256 reservesXIn, uint256 reservesYOut) = adjustAmountsByTickPrice(
            reservesWithoutUsersLiquidityX, reservesWithoutUsersLiquidityY, updatedReserveX, updatedReserveY, maxTick
        );

        uint256 excessBorrowedY =
            computeExpectedSwapOutAmountWithoutFees(mintXAdj * LTV / 100, reservesXIn, reservesYOut);

        uint256 borrowY = mintYAdj + excessBorrowedY;
        verifyMaxBorrow(tester, 0, borrowY);
    }

    function testALS_MaxBorrowXAgainstL() public {
        fixture.newBlock(1);
        uint256 mintX = 2e18;
        uint256 mintY = 0.5e18;
        fixture.transferTokensTo(tester, mintX, mintY);
        uint256 mintedL = fixture.mintFor(tester, mintX, mintY);

        fixture.newBlock(1);
        uint256 swapAmountYIn = 12e18;
        uint256 swapAmountXOut = computeExpectedSwapOutAmount(swapAmountYIn, initialY + mintY, initialX + mintX);

        fixture.swapYInXOut(tester, swapAmountYIn, swapAmountXOut);

        uint256 updatedReserveX = initialX + mintX - swapAmountXOut;
        uint256 updatedReserveY = initialY + mintY + swapAmountYIn;

        fixture.newBlock(1);
        pair.sync();

        (int16 minTick, int16 maxTick) = pair.exposed_getTickRange(); // get minTick for negative X (borrow X)
        require(minTick != maxTick, 'Calling pair.sync() in a new block, the minTick and maxTick should not be equal');

        {
            // How much x and y from mintL does the tester have now
            uint256 activeLiquidityAssets = fixture.activeLiquidityAssets();
            mintX = (mintedL * updatedReserveX) / activeLiquidityAssets;
            mintY = (mintedL * updatedReserveY) / activeLiquidityAssets;
        }

        // Slippage calculations for collateral liquidation excludes borrower's liquidity.
        uint256 reservesWithoutUsersLiquidityX = updatedReserveX - mintX;
        uint256 reservesWithoutUsersLiquidityY = updatedReserveY - mintY;

        (uint256 mintXAdj, uint256 mintYAdj) =
            adjustAmountsByTickPrice(mintX, mintY, updatedReserveX, updatedReserveY, minTick);

        (uint256 reservesXOut, uint256 reservesYIn) = adjustAmountsByTickPrice(
            reservesWithoutUsersLiquidityX, reservesWithoutUsersLiquidityY, updatedReserveX, updatedReserveY, minTick
        );

        uint256 excessBorrowedX =
            computeExpectedSwapOutAmountWithoutFees(mintYAdj * LTV / 100, reservesYIn, reservesXOut);
        uint256 borrowX = mintXAdj + excessBorrowedX;

        verifyMaxBorrow(tester, borrowX, 0, 5);
    }

    function testALS_MaxBorrowXAgainstLAfterSwapWithoutSync() public {
        fixture.newBlock(1);
        uint256 mintX = 2e18;
        uint256 mintY = 0.5e18;
        fixture.transferTokensTo(tester, mintX, mintY);
        uint256 mintedL = fixture.mintFor(tester, mintX, mintY);

        fixture.newBlock(1);
        uint256 swapAmountYIn = 12e18;
        uint256 swapAmountXOut = computeExpectedSwapOutAmount(swapAmountYIn, initialY + mintY, initialX + mintX);
        fixture.swapYInXOut(tester, swapAmountYIn, swapAmountXOut);

        uint256 updatedReserveX = initialX + mintX - swapAmountXOut;
        uint256 updatedReserveY = initialY + mintY + swapAmountYIn;

        (int16 minTick,) = pair.exposed_getTickRange(); // get minTick for negative X borrow X
        {
            // How much x and y from mintL does the tester have now
            uint256 activeLiquidityAssets = fixture.activeLiquidityAssets();
            mintX = (mintedL * updatedReserveX) / activeLiquidityAssets;
            mintY = (mintedL * updatedReserveY) / activeLiquidityAssets;
        }
        // Slippage calculations for collateral liquidation excludes borrower's liquidity.
        uint256 reservesWithoutUsersLiquidityX = updatedReserveX - mintX;
        uint256 reservesWithoutUsersLiquidityY = updatedReserveY - mintY;

        (uint256 mintXAdj, uint256 mintYAdj) =
            adjustAmountsByTickPrice(mintX, mintY, updatedReserveX, updatedReserveY, minTick);
        (uint256 reservesXOut, uint256 reservesYIn) = adjustAmountsByTickPrice(
            reservesWithoutUsersLiquidityX, reservesWithoutUsersLiquidityY, updatedReserveX, updatedReserveY, minTick
        );

        uint256 excessBorrowedX =
            computeExpectedSwapOutAmountWithoutFees(mintYAdj * LTV / 100, reservesYIn, reservesXOut);
        uint256 borrowX = mintXAdj + excessBorrowedX;

        verifyMaxBorrow(tester, borrowX, 0, 4);
    }

    function testALS_MaxBorrowXAgainstLWithMintExcessiveX() public {
        uint256 mintX = 10e18; // excessive mintX 8e18 more than required 2e18
        uint256 mintY = 0.5e18;

        fixture.newBlock(1);
        fixture.transferTokensTo(tester, mintX, mintY);
        uint256 mintedL = fixture.mintFor(tester, mintX, mintY);

        uint256 updatedReserveX = initialX + mintX;
        uint256 updatedReserveY = initialY + mintY;

        fixture.newBlock(1);
        pair.sync();

        (int16 minTick,) = pair.exposed_getTickRange(); // get minTick for negative X (borrow X)

        {
            // How much x and y from mintL does the tester have now
            uint256 activeLiquidityAssets = fixture.activeLiquidityAssets();
            mintX = (mintedL * updatedReserveX) / activeLiquidityAssets;
            mintY = (mintedL * updatedReserveY) / activeLiquidityAssets;
        }
        // Slippage calculations for collateral liquidation excludes borrower's liquidity.
        uint256 reservesWithoutUsersLiquidityX = updatedReserveX - mintX;
        uint256 reservesWithoutUsersLiquidityY = updatedReserveY - mintY;

        (uint256 mintXAdj, uint256 mintYAdj) =
            adjustAmountsByTickPrice(mintX, mintY, updatedReserveX, updatedReserveY, minTick);
        (uint256 reservesXOut, uint256 reservesYIn) = adjustAmountsByTickPrice(
            reservesWithoutUsersLiquidityX, reservesWithoutUsersLiquidityY, updatedReserveX, updatedReserveY, minTick
        );

        uint256 excessBorrowedX =
            computeExpectedSwapOutAmountWithoutFees(mintYAdj * LTV / 100, reservesYIn, reservesXOut);
        uint256 borrowX = mintXAdj + excessBorrowedX;

        verifyMaxBorrow(tester, borrowX, 0, 3);
    }

    function testALS_MaxBorrowXAgainstLWithSwapAndMintExcessiveX() public {
        uint256 mintX = 12e18; // excessive mintX 10e18 more than required 2e18
        uint256 mintY = 0.5e18;

        fixture.newBlock(1);
        fixture.transferTokensTo(tester, mintX, mintY);
        uint256 mintedL = fixture.mintFor(tester, mintX, mintY);

        uint256 updatedReserveX = initialX + mintX;
        uint256 updatedReserveY = initialY + mintY;

        uint256 swapAmountYIn = 1e18; // make a trade to generate different max tick
        uint256 swapAmountXOut = computeExpectedSwapOutAmount(swapAmountYIn, updatedReserveY, updatedReserveX);

        fixture.newBlock(1);
        fixture.swapYInXOut(tester, swapAmountYIn, swapAmountXOut);

        updatedReserveX = updatedReserveX - swapAmountXOut;
        updatedReserveY = updatedReserveY + swapAmountYIn;

        fixture.newBlock(1);
        pair.sync();

        (int16 minTick, int16 maxTick) = pair.exposed_getTickRange(); // get minTick for negative X (borrow X)
        require(minTick != maxTick, 'Calling pair.sync() in a new block, the minTick and maxTick should not be equal');

        {
            // How much x and y from mintL does the tester have now
            uint256 activeLiquidityAssets = fixture.activeLiquidityAssets();
            mintX = (mintedL * updatedReserveX) / activeLiquidityAssets;
            mintY = (mintedL * updatedReserveY) / activeLiquidityAssets;
        }

        // Slippage calculations for collateral liquidation excludes borrower's liquidity.
        uint256 reservesWithoutUsersLiquidityX = updatedReserveX - mintX;
        uint256 reservesWithoutUsersLiquidityY = updatedReserveY - mintY;

        (uint256 mintXAdj, uint256 mintYAdj) =
            adjustAmountsByTickPrice(mintX, mintY, updatedReserveX, updatedReserveY, minTick);
        (uint256 reservesXOut, uint256 reservesYIn) = adjustAmountsByTickPrice(
            reservesWithoutUsersLiquidityX, reservesWithoutUsersLiquidityY, updatedReserveX, updatedReserveY, minTick
        );

        uint256 excessBorrowedX =
            computeExpectedSwapOutAmountWithoutFees(mintYAdj * LTV / 100, reservesYIn, reservesXOut);

        uint256 borrowX = mintXAdj + excessBorrowedX;

        verifyMaxBorrow(tester, borrowX, 0, 2);
    }

    function testLtvBorrowXAgainstYAfterTwoSwaps() public {
        uint256 borrowX = 3e18;

        uint256 reserveXAfterFirstSwap;
        uint256 reserveYAfterFirstSwap;

        int16 previousMinTick;

        {
            fixture.newBlock(1);
            uint256 swapAmountYIn = 18e18;
            (, uint256 referenceReserveY) = pair.referenceReserves();
            uint256 swapAmountXOut =
                computeExpectedSwapOutAmount(swapAmountYIn, initialY, referenceReserveY, initialX, 0, 0);
            fixture.swapYInXOut(tester, swapAmountYIn, swapAmountXOut);
            reserveXAfterFirstSwap = initialX - swapAmountXOut;
            reserveYAfterFirstSwap = initialY + swapAmountYIn;

            fixture.newBlock(1);
            uint256 swapAmountXIn = swapAmountXOut / 3; // make a trade to generate different min tick
            (uint256 referenceReserveX,) = pair.referenceReserves();

            uint256 swapAmountYOut = computeExpectedSwapOutAmount(
                swapAmountXIn, reserveXAfterFirstSwap, referenceReserveX, reserveYAfterFirstSwap, 0, 0
            );
            fixture.swapXInYOut(tester, swapAmountXIn, swapAmountYOut);
        }

        (previousMinTick,) = pair.exposed_getTickRange();

        fixture.newBlock(1); // doing borrow in a new block
        pair.sync(); // call sync to update the reserves and tick observation
        (int16 minTick,) = pair.exposed_getTickRange(); // use minTick for negative X (borrow X)
        require(minTick != previousMinTick, 'minTick == previousMaxTick');

        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        (uint256 reservesXOut, uint256 reservesYIn) =
            adjustAmountsByTickPrice(reserveX, reserveY, reserveX, reserveY, minTick);

        uint256 collateralSwapped = computeExpectedSwapInAmountWithoutFees(borrowX, reservesYIn, reservesXOut);

        uint256 collateralY = Math.ceilDiv(100 * collateralSwapped, LTV);

        fixture.transferTokensTo(tester, 0, collateralY);
        fixture.depositFor(tester, 0, collateralY);

        verifyMaxBorrow(tester, borrowX, 0);
    }

    function testLtvBorrowYAgainstXAfterThreeRandomSwaps() public {
        uint256 borrowY = 3e18;

        int16 previousMaxTick;
        {
            uint256 updatedReserveX = initialX;
            uint256 updatedReserveY = initialY;

            fixture.newBlock(1);
            uint256 swapAmountXIn = 18e18;
            uint256 swapAmountYOut = computeExpectedSwapOutAmount(swapAmountXIn, updatedReserveX, updatedReserveY);
            fixture.swapXInYOut(tester, swapAmountXIn, swapAmountYOut);
            updatedReserveX += swapAmountXIn;
            updatedReserveY -= swapAmountYOut;

            fixture.newBlock(1);
            uint256 swapAmountYIn = 2e18;
            uint256 swapAmountXOut = computeExpectedSwapOutAmount(swapAmountYIn, initialY, initialX);
            fixture.swapYInXOut(tester, swapAmountYIn, swapAmountXOut);
            updatedReserveX -= swapAmountXOut;
            updatedReserveY += swapAmountYIn;

            fixture.newBlock(1);
            swapAmountYIn = 18e18; // make a trade to generate different max tick
            swapAmountXOut = computeExpectedSwapOutAmount(swapAmountYIn, updatedReserveX, updatedReserveY);
            fixture.swapYInXOut(tester, swapAmountYIn, swapAmountXOut);
            updatedReserveX -= swapAmountXOut;
            updatedReserveY += swapAmountYIn;
        }

        (, previousMaxTick) = pair.exposed_getTickRange();

        fixture.newBlock(1);
        pair.sync();

        (, int16 maxTick) = pair.exposed_getTickRange(); // use maxTick for negative Y (borrow Y)

        require(maxTick != previousMaxTick, 'after sync the maxTick should not be equal previousMaxTick');

        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();

        (uint256 reservesXIn, uint256 reservesYOut) =
            adjustAmountsByTickPrice(reserveX, reserveY, reserveX, reserveY, maxTick);

        uint256 collateralSwapped = computeExpectedSwapInAmountWithoutFees(borrowY, reservesXIn, reservesYOut);

        uint256 collateralX = Math.ceilDiv(100 * collateralSwapped, LTV);

        fixture.transferTokensTo(tester, collateralX, 0);
        fixture.depositFor(tester, collateralX, 0);

        verifyMaxBorrow(tester, 0, borrowY - 1);
    }

    function verifyMaxBorrow(address borrower, uint256 borrowX, uint256 borrowY) private {
        verifyMaxBorrow(borrower, borrowX, borrowY, 1);
    }

    function verifyMaxBorrow(address borrower, uint256 borrowX, uint256 borrowY, uint256 increment) private {
        if (borrowY == 0) {
            vm.expectRevert(Validation.AmmalgamLTV.selector);
            fixture.borrowFor(borrower, borrowX + increment, borrowY);
        } else if (borrowX == 0) {
            vm.expectRevert(Validation.AmmalgamLTV.selector);
            fixture.borrowFor(borrower, borrowX, borrowY + increment);
        } else {
            vm.expectRevert(Validation.AmmalgamLTV.selector);
            fixture.borrowFor(borrower, borrowX + increment, borrowY);
            vm.expectRevert(Validation.AmmalgamLTV.selector);
            fixture.borrowFor(borrower, borrowX, borrowY + increment);
        }

        // max borrow approved
        fixture.borrowFor(borrower, borrowX, borrowY);
    }
}
