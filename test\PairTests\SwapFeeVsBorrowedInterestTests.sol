// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {WAD} from '@morpho-org/morpho-blue/src/libraries/MathLib.sol';

import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {computeExpectedSwapOutAmount} from 'test/shared/utilities.sol';
import {BORROW_L} from 'contracts/interfaces/tokens/ITokenController.sol';
import {Q32} from 'contracts/libraries/constants.sol';

import {
    BORROW_L,
    BORROW_X,
    BORROW_Y,
    DEPOSIT_L,
    DEPOSIT_X,
    DEPOSIT_Y
} from 'contracts/interfaces/tokens/ITokenController.sol';

contract SwapFeeVsBorrowedInterestTests is Test {
    uint256 constant Q64 = 1 << 64;

    FactoryPairTestFixture private fixture;

    address private minter = vm.addr(1111);
    address private borrower = vm.addr(1112);
    uint256 private initialX = 8e18;
    uint256 private initialY = 2e18;
    address pair;

    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        pair = fixture.pairAddress();

        fixture.transferTokensTo(minter, initialX, initialY);
        fixture.mintForAndInitializeBlocks(minter, initialX, initialY);
    }

    function testBackOutSwapFeesForBorrowLiquidity() public {
        uint256 depositedL = Math.sqrt(initialX * initialY);
        // Borrow 3/4 of the deposited liquidity
        uint256 borrowedL = depositedL * 3 / 4;
        uint256 activeLiquidityAssets = depositedL; // this is lastActiveLiquidityAssets in the contract
        (uint256 _reserveXAssets, uint256 _reserveYAssets,) = fixture.pair().getReserves();
        uint256 _lastReserveLiquidity = Math.sqrt(_reserveXAssets * _reserveYAssets);
        {
            uint256 attackBalanceX = 1e19;

            washTradeHelper(minter, attackBalanceX);
        }
        // Calculate expected borrow of lx and ly
        (uint256 afterReserveXAssets, uint256 afterReserveYAssets,) = fixture.pair().getReserves();
        uint256 _currentReserveLiquidity = Math.sqrt(afterReserveXAssets * afterReserveYAssets);
        {
            uint256 collateralX = initialX * 100;
            uint256 collateralY = initialY * 100;
            fixture.transferTokensTo(borrower, collateralX, collateralY);
            fixture.depositFor(borrower, collateralX, collateralY);
        }

        (uint256 actualBorrowedLX, uint256 actualBorrowedLY) = fixture.borrowLiquidityFor(borrower, borrowedL);

        {
            uint256 expectedBorrowLX = calculateExpectedBorrow(
                borrowedL, afterReserveXAssets, _lastReserveLiquidity, _currentReserveLiquidity, activeLiquidityAssets
            );

            uint256 expectedBorrowLY = calculateExpectedBorrow(
                borrowedL, afterReserveYAssets, _lastReserveLiquidity, _currentReserveLiquidity, activeLiquidityAssets
            );

            assertEq(expectedBorrowLX, actualBorrowedLX, 'Expected borrow X should be equal to initial X');
            assertEq(expectedBorrowLY, actualBorrowedLY, 'Expected borrow Y should be equal to initial Y');
        }
    }

    function testBackOutSwapFeesForRepayBorrowedLiquidity() public {
        uint256 depositedL = Math.sqrt(initialX * initialY);
        uint256 collateralX = initialX * 100;
        uint256 collateralY = initialY * 100;
        // Borrow 3/4 of the deposited liquidity
        uint256 borrowedL = depositedL * 3 / 4;
        uint256 attackBalanceX = 1e19;
        {
            (uint256 _reserveXAssets, uint256 _reserveYAssets,) = fixture.pair().getReserves();
            (_reserveXAssets, _reserveYAssets,) = fixture.pair().getReserves();
        }

        fixture.transferTokensTo(borrower, collateralX, collateralY);
        fixture.depositFor(borrower, collateralX, collateralY);
        (uint256 actualBorrowedLX, uint256 actualBorrowedLY) = fixture.borrowLiquidityFor(borrower, borrowedL);

        uint256 _beforeReserveXAssets = initialX - actualBorrowedLX; // Rx_0
        uint256 _beforeReserveYAssets = initialY - actualBorrowedLY;

        // washtrade helper
        washTradeHelper(minter, attackBalanceX);

        // Calculate expected repay of lx and ly
        (uint256 afterReserveXAssets, uint256 afterReserveYAssets,) = fixture.pair().getReserves();

        {
            uint256 swapFeeGrowthQ32 = Math.sqrt(
                Q64 * afterReserveXAssets * afterReserveYAssets / (_beforeReserveXAssets * _beforeReserveYAssets)
            );
            uint256 expectedRepayX =
                Math.ceilDiv(afterReserveXAssets * actualBorrowedLX * Q32, _beforeReserveXAssets * swapFeeGrowthQ32);
            uint256 expectedRepayY =
                Math.ceilDiv(afterReserveYAssets * actualBorrowedLY * Q32, _beforeReserveYAssets * swapFeeGrowthQ32);

            fixture.transferTokensTo(
                borrower,
                expectedRepayX > actualBorrowedLX ? expectedRepayX - actualBorrowedLX : 0,
                expectedRepayY > actualBorrowedLY ? expectedRepayY - actualBorrowedLY : 0
            );

            fixture.repayLiquidityForNoEvent(borrower, expectedRepayX * 9 / 10, expectedRepayY * 9 / 10);
            uint256 sharesLeft = fixture.pair().tokens(BORROW_L).balanceOf(borrower);
            assertApproxEqRel(
                sharesLeft,
                borrowedL / 10,
                1e9,
                'The number of shares left should be close to 1/10th of the starting amount'
            );
            assertLe(
                expectedRepayX - actualBorrowedLX,
                attackBalanceX - fixture.tokenX().balanceOf(minter),
                'Attacker should have spent less than the borrower owed'
            );
            assertLt(expectedRepayY, actualBorrowedLY, 'Expected repay Y should be less than borrowed Y');
        }
    }

    function testBackOutSwapFeesForBorrowLiquidityAfterMint() public {
        uint256 depositedL = Math.sqrt(initialX * initialY);
        uint256 borrowedL = depositedL * 3 / 4;
        uint256 reserveLiquidityBeforeWashTrade;
        {
            (uint256 reserveXBeforeWashTrade, uint256 reserveYBeforeWashTrade,) = fixture.pair().getReserves();
            reserveLiquidityBeforeWashTrade = Math.sqrt(reserveXBeforeWashTrade * reserveYBeforeWashTrade);
            uint256 attackBalanceX = 2e19;
            washTradeHelper(minter, attackBalanceX);
        }
        {
            (uint256 reserveXAfterWashTrade, uint256 reserveYAfterWashTrade,) = fixture.pair().getReserves();
            uint256 mintLX;
            uint256 mintLY;
            mintLX = reserveXAfterWashTrade / 4;
            mintLY = reserveYAfterWashTrade / 4;
            fixture.transferTokensTo(minter, mintLX, mintLY);
            uint256 mintedL = fixture.mintFor(minter, mintLX, mintLY);
            depositedL += mintedL;

            assertApproxEqAbs(
                mintedL * 5, depositedL, 4, 'Expected minted liquidity should be 1/4 of the deposited liquidity'
            );
        }
        uint256 reserveLiquidityAfterMint;
        (uint256 reserveXAfterMint, uint256 reserveYAfterMint,) = fixture.pair().getReserves();
        {
            reserveLiquidityAfterMint = Math.sqrt(reserveXAfterMint * reserveYAfterMint);
        }
        {
            uint256 collateralX = initialX * 1000;
            uint256 collateralY = initialY * 1000;
            fixture.transferTokensTo(borrower, collateralX, collateralY);
            fixture.depositFor(borrower, collateralX, collateralY);
        }

        (uint256 actualBorrowedLX, uint256 actualBorrowedLY) = fixture.borrowLiquidityFor(borrower, borrowedL);
        uint256 reserveLiquidityAfterBorrow;
        {
            (uint256 reserveXAfterBorrow, uint256 reserveYAfterBorrow,) = fixture.pair().getReserves();
            reserveLiquidityAfterBorrow = Math.sqrt(reserveXAfterBorrow * reserveYAfterBorrow);
            // borrowedLXAssets = BLA * Rx * RL_0 / (RL_1 * ALA_0)
            (uint256 expectedBorrowLX) = calculateExpectedBorrow(
                borrowedL,
                reserveXAfterMint,
                reserveLiquidityBeforeWashTrade, // passing in reserve liquidity after mint since reserve is updated during mint
                reserveLiquidityAfterMint,
                Math.sqrt(initialX * initialY) // lastActiveLiquidityAssets
            );
            assertEq(expectedBorrowLX, actualBorrowedLX, 'Expected borrow X should be equal to actual X');

            (uint256 expectedBorrowLY) = calculateExpectedBorrow(
                borrowedL,
                reserveYAfterMint,
                reserveLiquidityBeforeWashTrade,
                reserveLiquidityAfterMint,
                Math.sqrt(initialX * initialY)
            );
            assertEq(expectedBorrowLY, actualBorrowedLY, 'Expected borrow Y should be equal to actual Y');
        }
    }

    function testBackOutSwapFeesForBorrowLiquidityWithoutExtraMint() public {
        uint256 depositedL = Math.sqrt(initialX * initialY);
        uint256 activeLiquidityAssets = depositedL;
        // Borrow 3/4 of the deposited liquidity
        uint256 borrowedL = depositedL * 3 / 4;

        (uint256 _reserveXBeforeWashTrade, uint256 _reserveYBeforeWashTrade,) = fixture.pair().getReserves();
        uint256 _lastReserveLiquidity = Math.sqrt(_reserveXBeforeWashTrade * _reserveYBeforeWashTrade);
        {
            uint256 attackBalanceX = 2e19;
            washTradeHelper(minter, attackBalanceX);
        }

        // Calculate expected borrow of lx and ly
        (uint256 reserveXAfterWashTrade, uint256 reserveYAfterWashTrade,) = fixture.pair().getReserves();
        uint256 reserveLiquidityAfterWashTrade = Math.sqrt(reserveXAfterWashTrade * reserveYAfterWashTrade);

        {
            uint256 collateralX = initialX * 1000;
            uint256 collateralY = initialY * 1000;
            fixture.transferTokensTo(borrower, collateralX, collateralY);
            fixture.depositFor(borrower, collateralX, collateralY);
            assertEq(
                depositedL * 3 / 4, borrowedL, 'Expected borrowed liquidity should be 3/4 of the deposited liquidity'
            );
        }
        {
            // borrowedLXAssets = BLA * Rx * RL_0 / (RL_1 * ALA_0)
            uint256 expectedBorrowLX = calculateExpectedBorrow(
                borrowedL,
                reserveXAfterWashTrade,
                _lastReserveLiquidity,
                reserveLiquidityAfterWashTrade,
                activeLiquidityAssets
            );

            uint256 expectedBorrowLY = calculateExpectedBorrow(
                borrowedL,
                reserveYAfterWashTrade,
                _lastReserveLiquidity,
                reserveLiquidityAfterWashTrade,
                activeLiquidityAssets
            );

            (uint256 actualBorrowedLX, uint256 actualBorrowedLY) = fixture.borrowLiquidityFor(borrower, borrowedL);

            assertEq(expectedBorrowLX, actualBorrowedLX, 'Expected borrow X should be equal to actual X');
            assertEq(expectedBorrowLY, actualBorrowedLY, 'Expected borrow Y should be equal to actual Y');
        }
    }

    function calculateExpectedBorrow(
        uint256 borrowedL,
        uint256 reserve,
        uint256 _lastReserveLiquidity,
        uint256 _currentReserveLiquidity,
        uint256 activeLiquidityAssets
    ) private pure returns (uint256 expectedBorrow) {
        // LX = BLA * Rx * RL_0 / (RL_1 * ALA_0)
        expectedBorrow =
            Math.mulDiv(borrowedL, reserve * _lastReserveLiquidity, _currentReserveLiquidity * activeLiquidityAssets);
    }

    function swapXIn(address user, uint256 amountXIn, uint256 amountYOut) private {
        vm.startPrank(user);
        fixture.tokenX().transfer(pair, amountXIn);
        fixture.pair().swap(0, amountYOut, user, '');
        vm.stopPrank();
    }

    function swapYIn(address user, uint256 amountYIn, uint256 amountXOut) private {
        vm.startPrank(user);
        fixture.tokenY().transfer(pair, amountYIn);
        fixture.pair().swap(amountXOut, 0, user, '');
        vm.stopPrank();
    }

    /**
     *
     * @notice Constant to increase slippage, otherwise wash trading will never deplete balance if
     *  this is $$998 / 1000$$ or greater, this test will run out of gas. By making it larger, we are
     *  multiplying how much fees accrue each swap, so 1 swap is equal to some order of magnitude
     *  more swaps
     */
    uint256 private constant ADDED_SLIPPAGE_NUMERATOR = 99;
    uint256 private constant ADDED_SLIPPAGE_DENOMINATOR = 100;

    function washTradeHelper(address washMinter, uint256 attackBalanceX) private {
        fixture.transferTokensTo(washMinter, attackBalanceX, 0);

        for (uint256 swapAmountXIn = attackBalanceX; swapAmountXIn > 0;) {
            (uint256 _reserveXAssets, uint256 _reserveYAssets,) = fixture.pair().getReserves();
            (uint256 referenceReserveX, uint256 referenceReserveY) = fixture.pair().referenceReserves();

            uint256 swapAmountYOut = ADDED_SLIPPAGE_NUMERATOR
                * computeExpectedSwapOutAmount(swapAmountXIn, _reserveXAssets, referenceReserveX, _reserveYAssets, 0, 0)
                / ADDED_SLIPPAGE_DENOMINATOR;
            if (swapAmountYOut == 0) {
                break;
            }
            swapXIn(washMinter, swapAmountXIn, swapAmountYOut);
            // undo the swap to wash trade
            uint256 swapYAmountIn = swapAmountYOut;
            uint256 swapXAmountOut = ADDED_SLIPPAGE_NUMERATOR
                * computeExpectedSwapOutAmount(swapYAmountIn, _reserveYAssets, referenceReserveY, _reserveXAssets, 0, 0)
                / ADDED_SLIPPAGE_DENOMINATOR;
            if (swapXAmountOut == 0) {
                break;
            }
            swapYIn(washMinter, swapYAmountIn, swapXAmountOut);
            swapAmountXIn = swapXAmountOut;
        }
    }
}
