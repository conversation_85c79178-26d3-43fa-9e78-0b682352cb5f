// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {IAmmalgamERC20} from 'contracts/interfaces/tokens/IAmmalgamERC20.sol';
import {ERC20BaseConfig} from 'contracts/tokens/ERC20Base.sol';
import {ITokenFactory} from 'contracts/interfaces/factories/ITokenFactory.sol';

event TokenCreated(address indexed token);
/**
 * @notice The stub factory contract is only used for hardhat testing purposes
 * @dev It is in the hardhat-stubs directory so that only this contract is compiled by hardhat,
 * see hardhat.config.ts.
 */

contract ERC20TokenFactoryStub {
    IAmmalgamERC20 public token;

    constructor(ITokenFactory tokenFactory, ERC20BaseConfig memory config) {
        token = tokenFactory.createToken(config, address(0));
    }

    function mintERC20Token(address sender, address to, uint256 assets, uint256 shares) public {
        token.ownerMint(sender, to, assets, shares);
    }
}
