// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.18;

import {Test} from 'forge-std/Test.sol';

import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {MathLib} from '@morpho-org/morpho-blue/src/libraries/MathLib.sol';

import {IPairHarness} from 'test/shared/FactoryPairTestFixture.sol';
import {BORROW_L, ROUNDING_UP} from 'contracts/interfaces/tokens/ITokenController.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';
import {Interest} from 'contracts/libraries/Interest.sol';
import {Convert} from 'contracts/libraries/Convert.sol';
import {Q128} from 'contracts/libraries/constants.sol';

import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {
    InterestFixture,
    SharesAndAssets,
    UserPosition,
    RepayWithdrawPosition
} from 'test/InterestTests/InterestFixture.sol';

contract InterestBorrowRepayLiquidityFuzzTests is Test {
    using MathLib for uint256;
    using Math<PERSON>ib for uint128;

    FactoryPairTestFixture public fixture;
    InterestFixture public interestFixture;
    IPairHarness public pair;

    address random = address(0xa0);
    address borrower1Addr = address(0xb1);
    address minter1Addr = address(0xd1);

    SharesAndAssets states;

    uint256 initialLX;
    uint256 initialLY;
    uint256 initialL;

    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, true);
        interestFixture = new InterestFixture(fixture);

        pair = fixture.pair();
    }

    function testFuzz_Interest_BorrowRepayPartial(
        uint256 _initialLX,
        uint256 _initialLY,
        uint8 repayPercentage
    ) public {
        initializePoolAndStubGeometricTWAP(_initialLX, _initialLY);

        repayPercentage = uint8(bound(repayPercentage, 1, 99));

        // Set up borrower position
        UserPosition memory borrowerPosition;
        borrowerPosition.userAddress = borrower1Addr;
        borrowerPosition.borrowLAssets = 30e18;
        borrowerPosition.depositXAssets = type(uint112).max / 3;

        // Borrow L assets
        interestFixture.createUserPosition(borrowerPosition);

        uint256 borrowedLX = borrowerPosition.borrowLAssets * initialLX / initialL;
        uint256 borrowedLY = borrowerPosition.borrowLAssets * initialLY / initialL;

        assertEq(fixture.tokenX().balanceOf(borrower1Addr), borrowedLX, 'borrowLX balance mismatch');
        assertEq(fixture.tokenY().balanceOf(borrower1Addr), borrowedLY, 'borrowLY balance mismatch');

        // Get the initial borrowed amounts
        uint256 initialBorrowedLAssets = pair.exposed_getAssets(BORROW_L, borrowerPosition.userAddress);
        uint256 initialBorrowedLShares = pair.tokens(BORROW_L).balanceOf(borrowerPosition.userAddress);

        // Get the pair state
        SharesAndAssets memory pairState = interestFixture.getPairStates();

        uint256 repayLAssetsAmount = initialBorrowedLAssets * repayPercentage / 100;

        // Calculate expected X and Y assets to repay using the protocol's formula
        uint256 expectedRepayXAmount = Math.ceilDiv(
            repayLAssetsAmount * pairState.reserveXAssets, pairState.depositLAssets - pairState.borrowLAssets
        );
        uint256 expectedRepayYAmount = Math.ceilDiv(
            repayLAssetsAmount * pairState.reserveYAssets, pairState.depositLAssets - pairState.borrowLAssets
        );

        // Calculate expected remaining shares after repayment
        uint256 expectedRemainingShares = initialBorrowedLShares * (100 - repayPercentage) / 100;

        uint256 convertedBorrowLAssets = Convert.toAssets(
            initialBorrowedLShares * repayPercentage / 100,
            pair.totalAssets()[BORROW_L],
            pair.tokens(BORROW_L).totalSupply(),
            !ROUNDING_UP
        );

        // Verify that `getAssets` and `toAssets` return the same value
        assertGe(
            repayLAssetsAmount, convertedBorrowLAssets, 'Borrowed L assets should match the converted value from shares'
        );

        // Execute the partial repayment
        interestFixture.verifyRepayAmountsAndRepay(
            borrowerPosition.userAddress,
            repayLAssetsAmount,
            expectedRepayXAmount,
            expectedRepayYAmount,
            expectedRemainingShares
        );

        // Get remaining borrowed assets after repayment
        uint256 remainingLAssets = pair.exposed_getAssets(BORROW_L, borrowerPosition.userAddress);

        // Verify that remaining assets are less than or equal to the expected amount
        assertLe(
            remainingLAssets,
            initialBorrowedLAssets - repayLAssetsAmount,
            'Remaining L assets should be less than or equal to the expected amount after repayment'
        );

        // Verify that the Total Borrowed Liquidity assets are less than or equal to the expected amount
        assertLe(
            pair.totalAssets()[BORROW_L],
            initialBorrowedLAssets - repayLAssetsAmount,
            'Total Borrowed Liquidity assets should be less than or equal to the expected amount after repayment'
        );

        // Verify that the remaining assets are less than or equal to the initial borrowed amount due to accrued interest
        assertLe(
            remainingLAssets,
            initialBorrowedLAssets * (100 - repayPercentage) / 100,
            'Remaining assets should be less than or equal to the initial borrowed amount due to interest accrual'
        );
    }

    function testFuzz_Interest_BorrowAccrueRepayBorrowVerifyShares(
        uint256 _initialLX,
        uint256 _initialLY,
        uint8 repayPercentage,
        uint256 duration
    ) public {
        initializePoolAndStubGeometricTWAP(_initialLX, _initialLY);

        repayPercentage = uint8(bound(repayPercentage, 1, 100));
        // Bound duration between 1 and 30 days
        duration = bound(duration, 1, 216_000) * 12; // 30 days * 86400s per day / 12s per block

        // Set up borrower position
        UserPosition memory borrowerPosition;
        borrowerPosition.userAddress = borrower1Addr;
        borrowerPosition.borrowLAssets = 30e18;
        borrowerPosition.depositXAssets = type(uint112).max / 3;

        // create borrower position to start earning interest
        interestFixture.createUserPosition(borrowerPosition);

        // Get the initial borrowed amounts
        uint256 initialBorrowedLAssets = pair.exposed_getAssets(BORROW_L, borrowerPosition.userAddress);
        uint256 initialBorrowedLShares = pair.tokens(BORROW_L).balanceOf(borrowerPosition.userAddress);

        // Roll block + accrue interest
        interestFixture.warpAndSkim(duration);

        // Calculate the L assets to repay using current borrowed assets which includes accrued interest
        uint256 currentBorrowedLAssets = pair.exposed_getAssets(BORROW_L, borrowerPosition.userAddress);
        assertGt(
            currentBorrowedLAssets,
            initialBorrowedLAssets,
            'Current borrowed L assets should be more than initial borrowed amount'
        );

        // Get the pair state
        SharesAndAssets memory pairState = interestFixture.getPairStates();

        uint256 repayLAssetsAmount = currentBorrowedLAssets * repayPercentage / 100;

        // Calculate expected X and Y assets to repay using the protocol's formula
        uint256 expectedRepayXAmount = Math.ceilDiv(
            repayLAssetsAmount * pairState.reserveXAssets, pairState.depositLAssets - pairState.borrowLAssets
        );
        uint256 expectedRepayYAmount = Math.ceilDiv(
            repayLAssetsAmount * pairState.reserveYAssets, pairState.depositLAssets - pairState.borrowLAssets
        );

        // Calculate expected remaining shares after repayment
        uint256 expectedRemainingShares = initialBorrowedLShares * (100 - repayPercentage) / 100;

        uint256 convertedBorrowLAssets = Convert.toAssets(
            initialBorrowedLShares * repayPercentage / 100,
            pair.totalAssets()[BORROW_L],
            pair.tokens(BORROW_L).totalSupply(),
            !ROUNDING_UP
        );

        // Verify that `getAssets` and `toAssets` return the same value
        assertEq(
            repayLAssetsAmount,
            convertedBorrowLAssets,
            'Borrowed L assets should be equal to the converted value from shares'
        );

        // Execute the partial repayment
        interestFixture.verifyRepayAmountsAndRepay(
            borrowerPosition.userAddress,
            repayLAssetsAmount,
            expectedRepayXAmount,
            expectedRepayYAmount,
            expectedRemainingShares
        );

        // Borrow the repaid amount again
        borrowerPosition.borrowLAssets = repayLAssetsAmount;
        borrowerPosition.depositXAssets = 0;

        // Borrow L assets
        interestFixture.createUserPosition(borrowerPosition);

        // Verify that the Total Borrowed Liquidity assets is less than or equal to the expected amount
        assertLe(
            pair.totalAssets()[BORROW_L],
            currentBorrowedLAssets,
            'Total Borrowed Liquidity assets should be less than or equal to the expected amount after borrowing again'
        );

        // Verify that the borrowed L shares are greater than or equal to the initial borrowed amount
        assertGe(
            pair.tokens(BORROW_L).balanceOf(borrowerPosition.userAddress),
            initialBorrowedLShares,
            'Borrowed L shares should be greater than or equal to the initial borrowed amount'
        );
    }

    function initializePoolAndStubGeometricTWAP(uint256 _initialLX, uint256 _initialLY) private {
        _initialLX = bound(_initialLX, 1, 9);
        _initialLY = bound(_initialLY, 1, 9);

        initialLX = _initialLX * _initialLX * 100e18;
        initialLY = _initialLY * _initialLY * 100e18;
        initialL = Math.sqrt(initialLX * initialLY);

        // Mint tokens
        fixture.transferTokensTo(random, initialLX, initialLY);
        fixture.mintFor(random, initialLX, initialLY);
    }
}
