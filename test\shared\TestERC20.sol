// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {ERC4626DepositToken} from 'contracts/tokens/ERC4626DepositToken.sol';
import {DEPOSIT_X} from 'contracts/interfaces/tokens/ITokenController.sol';
import {ERC20BaseConfig} from 'contracts/tokens/ERC20Base.sol';

import {StubValidator} from 'test/shared/StubValidator.sol';

contract TestERC20 is ERC4626DepositToken {
    constructor(
        uint256 _totalSupply
    )
        ERC4626DepositToken(
            ERC20BaseConfig(address(new StubValidator()), address(0), 'Ammalgam', 'AMM-V1', DEPOSIT_X),
            msg.sender
        )
    {
        _mint(msg.sender, _totalSupply);
    }
}
