// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {
    BOR<PERSON>W_L,
    BORROW_X,
    BORROW_Y,
    DEPOSIT_L,
    DEPOSIT_X,
    DEPOSIT_Y
} from 'contracts/interfaces/tokens/ITokenController.sol';
import {Test} from 'forge-std/Test.sol';

import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {IERC4626} from '@openzeppelin/contracts/interfaces/IERC4626.sol';
import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';

import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {IAmmalgamERC20} from 'contracts/interfaces/tokens/IAmmalgamERC20.sol';
import {IERC20DebtToken} from 'contracts/interfaces/tokens/IERC20DebtToken.sol';
import {AmmalgamPair} from 'contracts/AmmalgamPair.sol';
import {PluginRegistry} from 'contracts/tokens/PluginRegistry.sol';
import {INewTokensFactory} from 'contracts/interfaces/factories/INewTokensFactory.sol';
import {deployFactory, deployTokenFactory} from 'contracts/utils/deployHelper.sol';

import {StubERC20} from 'test/shared/StubErc20.sol';
import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {computeExpectedLiquidity} from 'test/shared/utilities.sol';

contract ERC4626DepositTest is Test {
    IAmmalgamFactory private factory;
    IAmmalgamPair private pair;

    FactoryPairTestFixture private fixture;
    PluginRegistry private pluginRegistry;

    address private random;
    address private tester;
    address private noDepositAddress = address(12_345);

    uint256 private constant initialX = 800e18;
    uint256 private constant initialY = 200e18;
    uint256 private constant initialDepositXAmount = 100e18;
    uint256 private constant initialDepositYAmount = 100e18;

    error AmmalgamDepositIsNotStrictlyBigger();
    error OwnableUnauthorizedAccount(address account);

    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        factory = fixture.factory();
        pair = fixture.pair();
        pluginRegistry = new PluginRegistry();

        random = address(99);
        tester = address(1111);

        fixture.transferTokensTo(random, initialX, initialY);
        fixture.mintForAndInitializeBlocks(random, initialX, initialY);

        // generate initial deposit shares for the random account
        IERC4626 depositTokenX = IERC4626(address(pair.tokens(DEPOSIT_X)));
        IERC4626 depositTokenY = IERC4626(address(pair.tokens(DEPOSIT_Y)));
        fixture.transferTokensTo(random, initialDepositXAmount, initialDepositYAmount);
        vm.startPrank(random);
        fixture.tokenX().approve(address(depositTokenX), initialDepositXAmount);
        fixture.tokenY().approve(address(depositTokenY), initialDepositYAmount);
        depositTokenX.deposit(initialDepositXAmount, random);
        depositTokenY.deposit(initialDepositYAmount, random);
        vm.stopPrank();
    }

    function testERC4626DepositAndWithdrawX() public {
        IERC4626 depositTokenX = IERC4626(address(pair.tokens(DEPOSIT_X)));

        uint256 totalSupply = initialDepositXAmount;
        uint256 totalAssets = initialDepositXAmount;
        uint256 testerDepositXAmount = 0.3e18;

        fixture.transferTokensTo(tester, testerDepositXAmount, 0);

        vm.startPrank(tester);
        fixture.tokenX().approve(address(depositTokenX), testerDepositXAmount);
        depositTokenX.deposit(testerDepositXAmount, tester);
        uint256 shares = fixture.pair().tokens(DEPOSIT_X).balanceOf(tester);

        assertEq(shares, testerDepositXAmount * totalSupply / totalAssets, 'shares should be equal');

        // withdraw
        uint256 withdrawAmount = testerDepositXAmount;
        depositTokenX.approve(address(depositTokenX), shares);
        depositTokenX.withdraw(withdrawAmount, tester, tester);

        vm.stopPrank();

        assertEq(totalAssets, initialDepositYAmount, 'totalAssets should be equal');
        assertEq(totalSupply, initialDepositYAmount, 'totalSupply should be equal');
        assertEq(fixture.tokenX().balanceOf(tester), testerDepositXAmount, 'tokenX balance should be withdrawal amount');
        assertEq(fixture.pair().tokens(DEPOSIT_X).balanceOf(tester), 0, 'Deposit Token balance in tester should be 0');
    }

    function testERC4626DepositAndWithdrawY() public {
        IERC4626 depositTokenY = IERC4626(address(pair.tokens(DEPOSIT_Y)));

        uint256 totalSupply = initialDepositYAmount;
        uint256 totalAssets = initialDepositYAmount;

        uint256 testerDepositYAmount = 0.3e18;
        fixture.transferTokensTo(tester, 0, testerDepositYAmount);

        vm.startPrank(tester);
        fixture.tokenY().approve(address(depositTokenY), testerDepositYAmount);
        depositTokenY.deposit(testerDepositYAmount, tester);
        uint256 shares = fixture.pair().tokens(DEPOSIT_Y).balanceOf(tester);

        assertEq(shares, testerDepositYAmount * totalSupply / totalAssets, 'shares should be equal');

        // withdraw
        depositTokenY.approve(address(depositTokenY), shares);
        depositTokenY.withdraw(shares, tester, tester);

        vm.stopPrank();

        assertEq(totalAssets, initialDepositYAmount, 'totalAssets should be equal');
        assertEq(totalSupply, initialDepositYAmount, 'totalSupply should be equal');
        assertEq(fixture.tokenY().balanceOf(tester), testerDepositYAmount, 'tokenY balance should be withdrawal amount');
        assertEq(fixture.pair().tokens(DEPOSIT_Y).balanceOf(tester), 0, 'Deposit Token balance in tester should be 0');
    }

    function testERC4626MintY() public {
        IERC4626 depositTokenY = IERC4626(address(pair.tokens(DEPOSIT_Y)));

        uint256 totalSupply = initialDepositYAmount;
        uint256 totalAssets = initialDepositYAmount;

        uint256 testerDepositYAmount = 0.3e18;
        fixture.transferTokensTo(tester, 0, testerDepositYAmount);

        // count the shares before the total states get changed.
        uint256 countMyShares = testerDepositYAmount * totalSupply / totalAssets;

        vm.startPrank(tester);
        fixture.tokenY().approve(address(depositTokenY), testerDepositYAmount);

        // mint shares
        depositTokenY.mint(countMyShares, tester);

        uint256 shares = fixture.pair().tokens(DEPOSIT_Y).balanceOf(tester);
        vm.stopPrank();

        totalSupply += testerDepositYAmount * totalSupply / totalAssets;
        totalAssets += testerDepositYAmount;

        assertEq(countMyShares, shares, 'countMyShares = shares');
        assertEq(countMyShares, depositTokenY.balanceOf(tester), 'shares should be equal');
        assertEq(totalAssets, depositTokenY.totalAssets(), 'totalAssets should be equal');
        assertEq(totalSupply, depositTokenY.totalSupply(), 'totalSupply should be equal');
        assertEq(fixture.tokenY().balanceOf(tester), 0, 'tokenY balance should be 0');
        assertEq(
            fixture.tokenY().balanceOf(address(pair)),
            initialY + totalAssets,
            'tokenY balance in pair should be initialY + totalAssets'
        );
    }

    function testERC4626DepositAndRedeemX() public {
        IERC4626 depositTokenX = IERC4626(address(pair.tokens(DEPOSIT_X)));

        uint256 totalSupply = initialDepositXAmount;
        uint256 totalAssets = initialDepositXAmount;
        uint256 testerDepositXAmount = 0.3e18;

        fixture.transferTokensTo(tester, testerDepositXAmount, 0);

        vm.startPrank(tester);
        fixture.tokenX().approve(address(depositTokenX), testerDepositXAmount);
        depositTokenX.deposit(testerDepositXAmount, tester);
        uint256 shares = fixture.pair().tokens(DEPOSIT_X).balanceOf(tester);

        // redeem shares
        depositTokenX.approve(address(depositTokenX), shares);
        depositTokenX.redeem(shares, tester, tester);

        vm.stopPrank();

        assertEq(totalAssets, initialDepositYAmount, 'totalAssets should be equal');
        assertEq(totalSupply, initialDepositYAmount, 'totalSupply should be equal');
        assertEq(fixture.tokenX().balanceOf(tester), testerDepositXAmount, 'tokenX balance should be withdrawal amount');
        assertEq(fixture.pair().tokens(DEPOSIT_X).balanceOf(tester), 0, 'Deposit Token balance in tester should be 0');
    }

    function testERC4626MintDebtX() public {
        IERC4626 depositTokenY = IERC4626(address(pair.tokens(DEPOSIT_Y)));

        uint256 totalSupply = initialDepositYAmount;
        uint256 totalAssets = initialDepositYAmount;

        uint256 testerDepositYAmount = 0.3e18;
        fixture.transferTokensTo(tester, 0, testerDepositYAmount);

        // count the shares before the total states get changed.
        uint256 countMyShares = testerDepositYAmount * totalSupply / totalAssets;

        vm.startPrank(tester);
        fixture.tokenY().approve(address(depositTokenY), testerDepositYAmount);

        // mint shares
        depositTokenY.mint(countMyShares, tester);

        uint256 shares = fixture.pair().tokens(DEPOSIT_Y).balanceOf(tester);

        assertEq(countMyShares, shares, 'countMyShares = shares');

        uint256 testerBorrowXShares = shares * 2 / 3;

        // borrow by calling mint
        IERC4626 borrowTokenX = IERC4626(address(pair.tokens(BORROW_X)));
        uint256 receivedAssets = borrowTokenX.mint(testerBorrowXShares, tester);
        vm.stopPrank();

        assertEq(fixture.tokenX().balanceOf(tester), receivedAssets, 'tester receives borrowed assets');
        assertEq(fixture.tokenX().balanceOf(address(borrowTokenX)), 0, 'debt token contract should not have assets');
        assertEq(pair.tokens(BORROW_X).balanceOf(address(borrowTokenX)), 0, 'debt token contract should not have debt');
        assertEq(pair.tokens(BORROW_X).balanceOf(tester), testerBorrowXShares, 'tester should have debt');
    }

    function testERC4626DepositDebtY() public {
        IERC4626 depositTokenX = IERC4626(address(pair.tokens(DEPOSIT_X)));

        uint256 totalSupply = initialDepositYAmount;
        uint256 totalAssets = initialDepositYAmount;

        uint256 testerDepositXAmount = 0.3e18;
        fixture.transferTokensTo(tester, testerDepositXAmount, 0);

        // count the shares before the total states get changed.
        uint256 countMyShares = testerDepositXAmount * totalSupply / totalAssets;

        vm.startPrank(tester);
        fixture.tokenX().approve(address(depositTokenX), testerDepositXAmount);

        // mint shares for deposit
        depositTokenX.mint(countMyShares, tester);

        uint256 testerBorrowYAssets = testerDepositXAmount * 1 / 10;

        // borrow by calling deposit
        IERC4626 borrowTokenY = IERC4626(address(pair.tokens(BORROW_Y)));

        uint256 shares = borrowTokenY.deposit(testerBorrowYAssets, tester);

        vm.stopPrank();

        assertEq(fixture.tokenY().balanceOf(tester), testerBorrowYAssets, 'debt token contract should not have assets');
        assertEq(fixture.tokenY().balanceOf(address(borrowTokenY)), 0, 'debt token contract should not have assets');
        assertEq(pair.tokens(BORROW_Y).balanceOf(address(borrowTokenY)), 0, 'debt token contract should not have debt');
        assertEq(pair.tokens(BORROW_Y).balanceOf(tester), shares, 'tester should have debt');
    }

    function testERC4626RedeemDebtX() public {
        IERC4626 depositTokenY = IERC4626(address(pair.tokens(DEPOSIT_Y)));

        uint256 totalSupply = initialDepositYAmount;
        uint256 totalAssets = initialDepositYAmount;

        uint256 testerDepositYAmount = 0.3e18;
        fixture.transferTokensTo(tester, 0, testerDepositYAmount);

        // count the shares before the total states get changed.
        uint256 countMyShares = testerDepositYAmount * totalSupply / totalAssets;

        vm.startPrank(tester);
        fixture.tokenY().approve(address(depositTokenY), testerDepositYAmount);

        // mint shares
        depositTokenY.mint(countMyShares, tester);

        uint256 shares = fixture.pair().tokens(DEPOSIT_Y).balanceOf(tester);

        uint256 testerBorrowXShares = shares * 2 / 3;

        // borrow by calling mint
        IERC4626 borrowTokenX = IERC4626(address(pair.tokens(BORROW_X)));
        uint256 receivedAssets = borrowTokenX.mint(testerBorrowXShares, tester);

        assertEq(fixture.tokenX().balanceOf(tester), receivedAssets, 'tester receives borrowed assets');
        assertEq(fixture.tokenX().balanceOf(address(borrowTokenX)), 0, 'debt token contract should not have assets');
        assertEq(pair.tokens(BORROW_X).balanceOf(address(borrowTokenX)), 0, 'debt token contract should not have debt');
        assertEq(pair.tokens(BORROW_X).balanceOf(tester), testerBorrowXShares, 'tester should have debt');

        // repay shares
        uint256 borrowedShares = testerBorrowXShares;
        fixture.tokenX().approve(address(borrowTokenX), receivedAssets);
        IERC4626(address(IERC20(address(borrowTokenX)))).redeem(borrowedShares, tester, tester);

        vm.stopPrank();

        assertEq(fixture.tokenX().balanceOf(tester), 0, 'tester should have no assets after repay');
        assertEq(pair.tokens(BORROW_X).balanceOf(tester), 0, 'tester should have no debt after repay');
    }

    function testERC4626WithdrawDebtY() public {
        IERC4626 depositTokenX = IERC4626(address(pair.tokens(DEPOSIT_X)));

        uint256 totalSupply = initialDepositYAmount;
        uint256 totalAssets = initialDepositYAmount;

        uint256 testerDepositXAmount = 0.3e18;
        fixture.transferTokensTo(tester, testerDepositXAmount, 0);

        // count the shares before the total states get changed.
        uint256 countMyShares = testerDepositXAmount * totalSupply / totalAssets;

        vm.startPrank(tester);
        fixture.tokenX().approve(address(depositTokenX), testerDepositXAmount);

        // mint shares for deposit
        depositTokenX.mint(countMyShares, tester);
        uint256 shares = fixture.pair().tokens(DEPOSIT_X).balanceOf(tester);

        uint256 testerBorrowYShares = shares * 1 / 10;

        // mint shares for borrow
        IERC4626 borrowTokenY = IERC4626(address(pair.tokens(BORROW_Y)));
        uint256 receivedAssets = borrowTokenY.mint(testerBorrowYShares, tester);

        assertEq(fixture.tokenY().balanceOf(tester), receivedAssets, 'tester receives borrowed assets');
        assertEq(fixture.tokenY().balanceOf(address(borrowTokenY)), 0, 'debt token contract should not have assets');
        assertEq(pair.tokens(BORROW_Y).balanceOf(address(borrowTokenY)), 0, 'debt token contract should not have debt');
        assertEq(pair.tokens(BORROW_Y).balanceOf(tester), testerBorrowYShares, 'tester should have debt');

        // repay assets
        uint256 borrowedAssets = receivedAssets;
        fixture.tokenY().approve(address(borrowTokenY), borrowedAssets);
        IERC4626(address(IERC20(address(borrowTokenY)))).withdraw(borrowedAssets, tester, tester);

        vm.stopPrank();

        assertEq(fixture.tokenY().balanceOf(tester), 0, 'tester should have no assets after repay');
        assertEq(pair.tokens(BORROW_Y).balanceOf(tester), 0, 'tester should have no debt after repay');
    }

    function testMintDebtXShouldFail() public {
        uint256 borrowXShares = 1e18;
        // mint shares for borrow
        IERC4626 borrowTokenX = IERC4626(address(pair.tokens(BORROW_X)));

        vm.expectRevert(AmmalgamDepositIsNotStrictlyBigger.selector);
        borrowTokenX.mint(borrowXShares, noDepositAddress);
    }

    function testMintDebtYShouldFail() public {
        uint256 borrowYShares = 1e18;
        // mint shares for borrow
        IERC4626 borrowTokenY = IERC4626(address(pair.tokens(BORROW_Y)));

        vm.expectRevert(AmmalgamDepositIsNotStrictlyBigger.selector);
        borrowTokenY.mint(borrowYShares, noDepositAddress);
    }

    function testMintDebtLShouldFail() public {
        uint256 borrowLShares = 1e18;

        vm.expectRevert(AmmalgamDepositIsNotStrictlyBigger.selector);
        fixture.borrowLiquidityFor(noDepositAddress, borrowLShares);
    }

    function testERC4626DirectCallOwnerMintShouldFail() public {
        IAmmalgamERC20 depositTokenX = IAmmalgamERC20(address(pair.tokens(DEPOSIT_X)));
        vm.expectRevert(abi.encodeWithSelector(OwnableUnauthorizedAccount.selector, address(this)));

        depositTokenX.ownerMint(tester, tester, 1, 1);

        IAmmalgamERC20 depositTokenL = IAmmalgamERC20(address(pair.tokens(DEPOSIT_L)));
        vm.expectRevert(abi.encodeWithSelector(OwnableUnauthorizedAccount.selector, address(this)));
        depositTokenL.ownerMint(tester, tester, 1, 1);

        IERC20DebtToken debtTokenX = IERC20DebtToken(address(pair.tokens(BORROW_X)));
        vm.expectRevert(abi.encodeWithSelector(OwnableUnauthorizedAccount.selector, address(this)));
        debtTokenX.ownerMint(tester, tester, 1, 1);

        IERC20DebtToken debtTokenL = IERC20DebtToken(address(pair.tokens(BORROW_L)));
        vm.expectRevert(abi.encodeWithSelector(OwnableUnauthorizedAccount.selector, address(this)));
        debtTokenL.ownerMint(tester, tester, 1, 1);
    }

    function testERC4626DirectCallOwnerBurnShouldFail() public {
        IAmmalgamERC20 depositTokenY = IAmmalgamERC20(address(pair.tokens(DEPOSIT_Y)));
        vm.expectRevert(abi.encodeWithSelector(OwnableUnauthorizedAccount.selector, address(this)));
        depositTokenY.ownerBurn(tester, tester, 1, 1);

        IAmmalgamERC20 depositTokenL = IAmmalgamERC20(address(pair.tokens(DEPOSIT_L)));
        vm.expectRevert(abi.encodeWithSelector(OwnableUnauthorizedAccount.selector, address(this)));
        depositTokenL.ownerBurn(tester, tester, 1, 1);

        IERC20DebtToken debtTokenY = IERC20DebtToken(address(pair.tokens(BORROW_Y)));
        vm.expectRevert(abi.encodeWithSelector(OwnableUnauthorizedAccount.selector, address(this)));
        debtTokenY.ownerBurn(tester, tester, 1, 1);

        IERC20DebtToken debtTokenL = IERC20DebtToken(address(pair.tokens(BORROW_L)));
        vm.expectRevert(abi.encodeWithSelector(OwnableUnauthorizedAccount.selector, address(this)));
        debtTokenL.ownerBurn(tester, tester, 1, 1);
    }

    function testCreateTokenFail() public {
        INewTokensFactory tokenFactory = deployTokenFactory();

        IERC20 tokenX = new StubERC20('TOKEN1', 'TN1', 1000e18);
        IERC20 tokenY = new StubERC20('TOKEN2', 'TN2', 1000e18);
        tokenFactory.createAllTokens(address(pair), address(pluginRegistry), address(tokenX), address(tokenY));

        address fakeTokenX = vm.addr(1111);
        address fakeTokenY = vm.addr(2222);
        tokenFactory.createAllTokens(address(pair), address(pluginRegistry), fakeTokenX, fakeTokenY);
    }
}
