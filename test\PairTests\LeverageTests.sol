// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {
    adjustAmountsByTickPrice,
    adjustAmountsByTickPriceForBorrowL,
    adjustAmountsByTickPriceForBorrow,
    adjustAmountsByTickPriceForDeposit
} from 'test/shared/utilities.sol';
import {Validation} from 'contracts/libraries/Validation.sol';

contract LeverageTests is Test {
    IAmmalgamFactory private factory;
    IAmmalgamPair private pair;
    address private pairAddress;

    address private random;
    address private tester = vm.addr(1112);

    uint256 private constant initialX = 8000e18;
    uint256 private constant initialY = 2000e18;
    uint256 private constant initialLiquidity = 4000e18;
    uint256 private constant initialXPerY = initialX / initialY;
    uint8 private constant ALLOWED_LIQUIDITY_LEVERAGE = 100;

    FactoryPairTestFixture private fixture;

    function setUp() public {
        random = vm.addr(1111);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        factory = fixture.factory();
        pair = fixture.pair();
        pairAddress = address(pair);

        fixture.transferTokensTo(random, initialX, initialY);
        fixture.mintForAndInitializeBlocks(random, initialX, initialY);
    }

    function testLeverageBorrowXYAgainstL() public {
        uint256 mintX = 4e18;
        uint256 mintY = 1e18;
        // max borrow approved
        uint256 borrowX = mintX - mintX / ALLOWED_LIQUIDITY_LEVERAGE;
        uint256 borrowY = mintY - mintY / ALLOWED_LIQUIDITY_LEVERAGE;

        (borrowX, borrowY) = adjustAmountsByTickPriceForBorrow(borrowX, borrowY, initialX, initialY);

        fixture.transferTokensTo(tester, mintX, mintY);
        fixture.mintFor(tester, mintX, mintY);

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.borrowFor(tester, borrowX + 1, borrowY);

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.borrowFor(tester, borrowX, borrowY + 1);

        fixture.borrowFor(tester, borrowX, borrowY);
    }

    function testLeverageBorrowLAgainstXY() public {
        uint256 collateralX = 4e18;
        uint256 collateralY = 1e18;

        uint256 collateralL = Math.sqrt(collateralX * collateralY);

        (collateralX, collateralY) = adjustAmountsByTickPriceForDeposit(collateralX, collateralY, initialX, initialY);

        // depositX and depositY will be converted to L by sqrtPriceX96, thus it needs to adjust by tick price
        fixture.transferTokensTo(tester, collateralX, collateralY);
        fixture.depositFor(tester, collateralX, collateralY);

        // calculate the max borrowable liquidity
        uint256 borrowLiquidity = collateralL - collateralL / ALLOWED_LIQUIDITY_LEVERAGE;

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.borrowLiquidityFor(tester, borrowLiquidity + 1);

        fixture.borrowLiquidityFor(tester, borrowLiquidity - 3);
    }

    function testLeverageBorrowLAgainstL() public {
        uint256 mintX = 4e18;
        uint256 mintY = 1e18;

        fixture.transferTokensTo(tester, mintX, mintY);
        fixture.mintFor(tester, mintX, mintY);

        // calculate the max borrowable liquidity
        uint256 collateralL = Math.sqrt(mintX * mintY);
        uint256 borrowLiquidity = collateralL - collateralL / ALLOWED_LIQUIDITY_LEVERAGE;

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.borrowLiquidityFor(tester, borrowLiquidity + 1);

        fixture.borrowLiquidityFor(tester, borrowLiquidity);
    }

    function testLeverageBurnLWithLAgainstL() public {
        uint256 mintL = 2e18;
        uint256 mintX = (mintL * initialX) / initialLiquidity;
        uint256 mintY = (mintL * initialY) / initialLiquidity;
        uint256 borrowL = 1e18;

        //  calculate collateral from borrow. borrowL * (1 - 1/100)
        uint256 requiredCollateralL =
            Math.ceilDiv((borrowL * ALLOWED_LIQUIDITY_LEVERAGE), (ALLOWED_LIQUIDITY_LEVERAGE - 1));

        fixture.transferTokensTo(tester, mintX, mintY);
        fixture.mintFor(tester, mintX, mintY);

        fixture.borrowLiquidityFor(tester, borrowL);

        uint256 burnL = mintL - requiredCollateralL;

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.burnFor(tester, burnL + 1);

        fixture.burnFor(tester, burnL);
    }

    function testLeverageMultipleBorrowL() public {
        uint256 mintL = 2e18;
        uint256 mintX = (mintL * initialX) / initialLiquidity;
        uint256 mintY = (mintL * initialY) / initialLiquidity;
        uint256 borrowL = 1e18;

        //  calculate collateral from borrow. borrowL * (1 - 1/100)
        uint256 requiredCollateralL =
            Math.ceilDiv((borrowL * ALLOWED_LIQUIDITY_LEVERAGE), (ALLOWED_LIQUIDITY_LEVERAGE - 1));

        fixture.transferTokensTo(tester, mintX, mintY);
        fixture.mintFor(tester, mintX, mintY);

        fixture.borrowLiquidityFor(tester, borrowL);

        uint256 newCollateral = mintL - requiredCollateralL;
        uint256 newBorrowL = newCollateral - newCollateral / ALLOWED_LIQUIDITY_LEVERAGE;

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.borrowLiquidityFor(tester, newBorrowL + 1);

        fixture.borrowLiquidityFor(tester, newBorrowL);
    }

    function testLeverageMaxBorrowL() public {
        uint256 collateralL = 2e18;
        uint256 mintX = (collateralL * initialX) / initialLiquidity;
        uint256 mintY = (collateralL * initialY) / initialLiquidity;

        uint256 maxBorrow = collateralL - collateralL / ALLOWED_LIQUIDITY_LEVERAGE;

        fixture.transferTokensTo(tester, mintX, mintY);
        fixture.mintFor(tester, mintX, mintY);

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.borrowLiquidityFor(tester, maxBorrow + 1); // make sure it fails

        fixture.borrowLiquidityFor(tester, maxBorrow);
    }

    function testLeverageBurnWithXYAgainstL() public {
        uint256 mintX = 8e18;
        uint256 mintY = 2e18;
        uint256 borrowX = mintX / 2;
        uint256 borrowY = mintY / 2;

        uint256 mintL = Math.sqrt(mintX * mintY);
        uint256 borrowInL = Math.sqrt(borrowX * borrowY);

        fixture.transferTokensTo(tester, mintX, mintY);
        fixture.mintFor(tester, mintX, mintY);

        (borrowX, borrowY) = adjustAmountsByTickPriceForBorrow(borrowX, borrowY, initialX, initialY);

        fixture.borrowFor(tester, borrowX, borrowY);

        //  calculate collateral from borrow. borrowL * (1 - 1/100)
        uint256 requiredCollateralL =
            Math.ceilDiv((borrowInL * ALLOWED_LIQUIDITY_LEVERAGE), (ALLOWED_LIQUIDITY_LEVERAGE - 1));

        uint256 burnL = mintL - requiredCollateralL;

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.burnFor(tester, burnL + 2);

        fixture.burnFor(tester, burnL + 1);
    }

    function testLeverageBorrowLAndWithdraw() public {
        uint256 depositX = 4e18;
        uint256 depositY = 1e18;

        uint256 depositL = Math.sqrt(depositX * depositY);

        // depositX and depositY will be converted to L by sqrtPriceX96, thus it needs to adjust by tick price
        (depositX, depositY) = adjustAmountsByTickPriceForDeposit(depositX, depositY, initialX, initialY);

        fixture.transferTokensTo(tester, depositX, depositY);
        fixture.depositFor(tester, depositX, depositY);

        // calculate the max borrowable liquidity
        uint256 borrowLiquidity = depositL - depositL / ALLOWED_LIQUIDITY_LEVERAGE;

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.borrowLiquidityFor(tester, borrowLiquidity + 1);

        fixture.borrowLiquidityFor(tester, borrowLiquidity - 2);

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.withdrawFor(tester, 0, 1);

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.withdrawFor(tester, 4, 0);
    }

    function testLeverageBorrowLAgainstX() public {
        uint256 depositX = 4e18;
        uint256 depositY = 1e18;

        uint256 depositL = Math.sqrt(depositX * depositY);

        // depositX and depositY will be converted to L by sqrtPriceX96, thus it needs to adjust by tick price
        (depositX, depositY) = adjustAmountsByTickPriceForDeposit(depositX, depositY, initialX, initialY);

        // calculate the max borrowable liquidity

        uint256 borrowLiquidity = depositL - depositL / ALLOWED_LIQUIDITY_LEVERAGE;

        fixture.transferTokensTo(tester, depositX, depositY);
        fixture.depositFor(tester, depositX - 1, depositY);

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.borrowLiquidityFor(tester, borrowLiquidity);

        fixture.depositFor(tester, 1, 0);
        fixture.borrowLiquidityFor(tester, borrowLiquidity - 3);
    }

    function testLeverageBorrowLAgainstY() public {
        uint256 depositX = 4e18;
        uint256 depositY = 1e18;

        uint256 depositL = Math.sqrt(depositX * depositY);

        // depositX and depositY will be converted to L by sqrtPriceX96, thus it needs to adjust by tick price
        (depositX, depositY) = adjustAmountsByTickPriceForDeposit(depositX, depositY, initialX, initialY);

        // calculate the max borrowable liquidity
        uint256 borrowLiquidity = depositL - depositL / ALLOWED_LIQUIDITY_LEVERAGE;

        fixture.transferTokensTo(tester, depositX, depositY);
        fixture.depositFor(tester, depositX, depositY - 1);

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.borrowLiquidityFor(tester, borrowLiquidity);

        fixture.depositFor(tester, 0, 1);

        fixture.borrowLiquidityFor(tester, borrowLiquidity - 2);
    }

    function testBorrowXAndY_againstK() public {
        uint256 tokenXAmount = 8e18;
        uint256 tokenYAmount = 2e18;

        fixture.transferTokensTo(tester, tokenXAmount, tokenYAmount);
        fixture.mintFor(tester, tokenXAmount, tokenYAmount);

        // Calculate the proper max borrow amounts
        uint256 borrowXAmount = tokenXAmount - tokenXAmount / ALLOWED_LIQUIDITY_LEVERAGE;
        uint256 borrowYAmount = tokenYAmount - tokenYAmount / ALLOWED_LIQUIDITY_LEVERAGE;

        (borrowXAmount, borrowYAmount) =
            adjustAmountsByTickPriceForBorrow(borrowXAmount, borrowYAmount, initialX, initialY);

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.borrowFor(tester, borrowXAmount + 1, borrowYAmount);

        vm.expectRevert(Validation.AmmalgamTooMuchLeverage.selector);
        fixture.borrowFor(tester, borrowXAmount, borrowYAmount + 1);

        fixture.borrowFor(tester, borrowXAmount, borrowYAmount);
    }
}
