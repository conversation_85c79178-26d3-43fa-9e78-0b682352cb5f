// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {Q64} from 'contracts/libraries/constants.sol';
import {QuadraticSwapFees} from 'contracts/libraries/QuadraticSwapFees.sol';

import {FactoryPairTestFixture, IPairHarness, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';

contract QuadraticSwapFeesTests is Test {
    IPairHarness private pair;
    FactoryPairTestFixture private fixture;

    uint256 private initialMintX;
    uint256 private initialMintY;
    uint256 private k;

    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        pair = fixture.pair();

        initialMintX = 9e18;
        initialMintY = 1e18;
        k = initialMintX * initialMintY;
    }

    function testFeeFromReferenceSwap100Bips() public view {
        uint256 in1 = getSwapInAmount(initialMintX, 100);

        uint256 swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(in1, initialMintX, initialMintX);

        assertEq(swapFee, 20 * Q64);
    }

    function testFeeFromReferenceSwap150bips() public view {
        uint256 in1 = getSwapInAmount(initialMintX, 150);
        uint256 swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(in1, initialMintX, initialMintX);

        assertEq(swapFee, 30 * Q64);
    }

    function testFeeFromReferenceSwapToMaxQuadraticGrowth() public view {
        uint256 in1 = 2 * initialMintX;
        uint256 swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(in1, initialMintX, initialMintX);

        assertEq(swapFee, 4000 * Q64);
    }

    function testFeeFromReferenceSwapPastQuadraticGrowth() public view {
        uint256 in1 = initialMintX * 8 / 3;
        uint256 swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(in1, initialMintX, initialMintX);

        assertEq(swapFee, 5000 * Q64);
    }

    function testFeeFrom100BipsToSwap100Bips() public view {
        uint256 in1 = getSwapInAmount(initialMintX, 100);
        uint256 swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(in1, initialMintX, initialMintX + in1);

        assertEq(swapFee, 60 * Q64);
    }

    function testFeeFrom100BipsToSwap150Bips() public view {
        uint256 in1 = getSwapInAmount(initialMintX, 100);
        uint256 in2 = getSwapInAmount(initialMintX, 150);
        uint256 swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(in2, initialMintX, initialMintX + in1);

        assertEq(swapFee, 70 * Q64);
    }

    function testFeeFrom100BipsToMaxQuadraticGrowth() public view {
        uint256 in1 = getSwapInAmount(initialMintX, 100);
        uint256 in2 = 2 * initialMintX - 2 * in1; // 3 * MX - in1 - currentReserve = 3 * MX - in1 - (MX + in1) = 2 * MX - 2 * in1
        uint256 swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(in2, initialMintX, initialMintX + in1);

        assertEq(swapFee, 4000 * Q64);
    }

    function testFeeFrom100BipsToPastQuadraticGrowth() public view {
        uint256 in1 = getSwapInAmount(initialMintX, 100);
        uint256 in2 = initialMintX * 8 / 3 - 2 * in1; // 24 + MX - in1 - currentReserve = 24 + MX - in1 - (MX + in1) = 24 - 2 * in1
        uint256 swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(in2, initialMintX, initialMintX + in1);

        assertEq(swapFee, 5000 * Q64);
    }

    function testFeeFrom1000BipsToPastQuadraticGrowth() public view {
        uint256 in1 = getSwapInAmount(initialMintX, 1000);
        uint256 in2 = initialMintX * 8 / 3 - 2 * in1; // 24 + MX - in1 - currentReserve = 24 + MX - in1 - (MX + in1) = 24 - 2 * in1
        uint256 swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(in2, initialMintX, initialMintX + in1);

        assertEq(swapFee, 5000 * Q64);
    }

    function testFeeFrom100BipsBack50Bips() public view {
        uint256 in1 = getSwapInAmount(initialMintX, 100);
        uint256 swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(in1, initialMintX, initialMintX);

        uint256 currentReserveY = getCurrentReserveOut(in1, swapFee, initialMintX, initialMintY);
        uint256 back2 = getSwapInAmount(currentReserveY, 50);
        swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(back2, initialMintY, currentReserveY);

        assertEq(swapFee, QuadraticSwapFees.MIN_FEE_Q64);
    }

    function testFeeFrom100BipsBackToReference() public view {
        uint256 in1 = getSwapInAmount(initialMintX, 100);
        uint256 swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(in1, initialMintX, initialMintX);

        uint256 currentReserveY = getCurrentReserveOut(in1, swapFee, initialMintX, initialMintY);

        uint256 back2 = initialMintY - currentReserveY;
        swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(back2, initialMintY, currentReserveY);

        assertEq(swapFee, QuadraticSwapFees.MIN_FEE_Q64);
    }

    function testFeeFrom100BipsBack200Bips() public view {
        uint256 in1 = getSwapInAmount(initialMintX, 100);
        uint256 swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(in1, initialMintX, initialMintX);

        uint256 currentReserveY = getCurrentReserveOut(in1, swapFee, initialMintX, initialMintY);
        uint256 back2 = getSwapInAmount(currentReserveY, 200);
        back2 = getSwapInAmount(initialMintY, 100) + initialMintY - currentReserveY;
        swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(back2, initialMintY, currentReserveY);

        assertEq(swapFee, 20 * Q64 * (back2 - (initialMintY - currentReserveY)) / back2);
    }

    function testFeeFrom200BipsBackToMaxQuadraticGrowth() public view {
        uint256 in1 = getSwapInAmount(initialMintX, 200);
        uint256 swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(in1, initialMintX, initialMintX);

        uint256 currentReserveY = getCurrentReserveOut(in1, swapFee, initialMintX, initialMintY);
        uint256 back2 = 2 * initialMintY + (initialMintY - currentReserveY);
        swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(back2, initialMintY, currentReserveY);

        assertEq(swapFee, 4000 * Q64 * (back2 - (initialMintY - currentReserveY)) / back2);
    }

    function testFeeFrom300BipsBackToPastQuadraticGrowth() public view {
        uint256 in1 = getSwapInAmount(initialMintX, 300);
        uint256 swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(in1, initialMintX, initialMintX);

        uint256 currentReserveY = getCurrentReserveOut(in1, swapFee, initialMintX, initialMintY);
        uint256 back2 = initialMintY * 8 / 3 + (initialMintY - currentReserveY);
        swapFee = QuadraticSwapFees.calculateSwapFeeBipsQ64(back2, initialMintY, currentReserveY);

        assertApproxEqRel(swapFee, 5000 * Q64 * (back2 - (initialMintY - currentReserveY)) / back2, 1);
    }

    function getSwapInAmount(uint256 inReserve, uint256 bips) private pure returns (uint256) {
        return inReserve * bips / 10_000;
    }

    function getCurrentReserveOut(
        uint256 swapIn,
        uint256 fee,
        uint256 reserveIn,
        uint256 reserveOut
    ) private pure returns (uint256) {
        return reserveIn * reserveOut
            / (reserveIn + swapIn * (QuadraticSwapFees.BIPS_Q64 - fee) / QuadraticSwapFees.BIPS_Q64);
    }
}
