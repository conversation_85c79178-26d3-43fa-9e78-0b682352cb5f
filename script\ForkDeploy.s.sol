// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import 'forge-std/Script.sol';

import {AmmalgamFactory} from 'contracts/factories/AmmalgamFactory.sol';
import {PluginRegistry} from 'contracts/tokens/PluginRegistry.sol';
import {deployFactory} from 'contracts/utils/deployHelper.sol';

contract ForkDeploy is Script {
    function run() external {
        string memory MAINNET_RPC_URL = vm.envString('MAINNET_RPC_URL');
        uint256 mainnetFork = vm.createFork(MAINNET_RPC_URL);
        vm.selectFork(mainnetFork);
        string memory mnemonic = vm.envString('MNEMONIC');
        uint256 privateKey = vm.deriveKey(mnemonic, 0);
        address deployer = vm.addr(privateKey);

        vm.startBroadcast(deployer);

        AmmalgamFactory factory = deployFactory(deployer);

        vm.stopBroadcast();

        console2.log('Factory Address:', address(factory));
    }
}
