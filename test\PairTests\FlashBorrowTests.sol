// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';

import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';
import {SafeERC20} from '@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {RevertReasonParser} from 'lib/1inch/solidity-utils/contracts/libraries/RevertReasonParser.sol';

import {ICallback} from 'contracts/interfaces/callbacks/IAmmalgamCallee.sol';
import {IERC20DebtToken} from 'contracts/interfaces/tokens/IERC20DebtToken.sol';
import {IAmmalgamERC20} from 'contracts/interfaces/tokens/IAmmalgamERC20.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {DEPOSIT_L, DEPOSIT_X, DEPOSIT_Y, BORROW_X, BORROW_Y} from 'contracts/interfaces/tokens/ITokenController.sol';

import {IPairHarness, FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {
    adjustAmountsByTickPriceForBorrow,
    adjustAmountsByTickPriceForDeposit,
    adjustAmountsByTickPriceForBorrowL,
    adjustAmountsByTickPrice,
    computeExpectedSwapOutAmount,
    toHexCapitalString
} from 'test/shared/utilities.sol';
import {Validation} from 'contracts/libraries/Validation.sol';

contract FLashBorrowTests is Test {
    using SafeERC20 for IERC20;
    using SafeERC20 for IAmmalgamERC20;

    FactoryPairTestFixture private fixture;
    address private tester;
    address private pairAddress;
    IPairHarness private pair;
    uint256 private initialX = 2e18;
    uint256 private initialY = 8e18;

    function setUp() public {
        address random = vm.addr(99);
        tester = vm.addr(100);
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        fixture.transferTokensTo(random, initialX, initialY);
        fixture.mintForAndInitializeBlocks(random, initialX, initialY);
        pairAddress = fixture.pairAddress();
        pair = fixture.pair();
    }

    function testFlashBorrowX() public {
        ExerciseArguments memory args;
        args.borrowX = 0.75e18;
        args.depositY = 8e18;

        exerciseTestee(args);

        // check pair
        FactoryPairTestFixture.ExpectedPairState memory expectPair;
        expectPair.tokenX = initialX - args.borrowX;
        expectPair.tokenY = initialY + args.depositY;
        expectPair.reserveXAssets = initialX;
        expectPair.reserveYAssets = initialY;

        // check tester
        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceX = args.borrowX;
        expectTester.pairDepositedY = args.depositY;
        expectTester.pairBorrowedX = args.borrowX;

        verifyFlashBorrow(expectPair, expectTester);
    }

    function testFlashBorrowY() public {
        ExerciseArguments memory args;
        args.borrowY = 3e18;
        args.depositX = 2e18;

        exerciseTestee(args);

        // check pair
        FactoryPairTestFixture.ExpectedPairState memory expectPair;
        expectPair.tokenX = initialX + args.depositX;
        expectPair.tokenY = initialY - args.borrowY;
        expectPair.reserveXAssets = initialX;
        expectPair.reserveYAssets = initialY;

        // check tester
        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceY = args.borrowY;
        expectTester.pairDepositedX = args.depositX;
        expectTester.pairBorrowedY = args.borrowY;

        verifyFlashBorrow(expectPair, expectTester);
    }

    function testFlashBorrowXYWithMint() public {
        ExerciseArguments memory args;
        args.borrowX = 1e18;
        args.borrowY = 4e18;
        args.mintX = 2e18;
        args.mintY = 8e18;

        exerciseTestee(args);

        // check pair
        FactoryPairTestFixture.ExpectedPairState memory expectPair;
        expectPair.tokenX = initialX + args.mintX - args.borrowX;
        expectPair.tokenY = initialY + args.mintY - args.borrowY;
        expectPair.reserveXAssets = initialX + args.mintX;
        expectPair.reserveYAssets = initialY + args.mintY;

        // check tester
        FactoryPairTestFixture.ExpectedAddressState memory expectTester;
        expectTester.toCheck = tester;
        expectTester.balanceX = args.borrowX;
        expectTester.balanceY = args.borrowY;
        expectTester.pairLiquidity = 4e18; // sqrt(2 * 8)
        expectTester.pairBorrowedX = args.borrowX;
        expectTester.pairBorrowedY = args.borrowY;

        verifyFlashBorrow(expectPair, expectTester);
    }

    struct ExerciseArguments {
        uint256 depositX;
        uint256 depositY;
        uint256 mintX;
        uint256 mintY;
        uint256 borrowX;
        uint256 borrowY;
    }

    function exerciseTestee(
        ExerciseArguments memory args
    ) private {
        ICallback callee = getReentryDelegatedCall(this.depositAndMintCallback);
        fixture.transferTokensTo(address(callee), args.depositX + args.mintX, args.depositY + args.mintY);

        bytes memory data = abi.encode(args.depositX, args.depositY, args.mintX, args.mintY);

        vm.startPrank(tester);
        fixture.pair().borrow(address(callee), args.borrowX, args.borrowY, data);
        vm.stopPrank();
    }

    function depositAndMintCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory data
    ) external {
        _fixture.transferTokensTo(sender, amountX, amountY);

        (uint256 depositX, uint256 depositY, uint256 mintX, uint256 mintY) =
            abi.decode(data, (uint256, uint256, uint256, uint256));
        if (depositX > 0) {
            _fixture.tokenX().transfer(address(_fixture.pair()), depositX);
        }
        if (depositY > 0) {
            _fixture.tokenY().transfer(address(_fixture.pair()), depositY);
        }

        _fixture.pair().deposit(sender);

        if (mintX > 0 && mintY > 0) {
            // mint liquidity
            _fixture.tokenX().transfer(address(_fixture.pair()), mintX);
            _fixture.tokenY().transfer(address(_fixture.pair()), mintY);
            _fixture.pair().mint(sender);
        }
    }

    function verifyFlashBorrow(
        FactoryPairTestFixture.ExpectedPairState memory expectedPairState,
        FactoryPairTestFixture.ExpectedAddressState memory expectedTester
    ) private view {
        fixture.verifyPair(expectedPairState);
        fixture.verifyAddress(expectedTester);
    }

    function testFlashBorrowReentryWorksLikeBorrowingTwice() public {
        uint256 amountX = 1e18;
        uint256 amountY = 4e18;
        uint256 borrowX = 0.1e18;
        uint256 borrowY = 0.4e18;

        fixture.transferTokensTo(tester, amountX, amountY);
        fixture.mintFor(tester, amountX, amountY);

        ICallback callee = new ReentryDelegatedCall(fixture, this.reentryBorrowAgainCallback);

        vm.startPrank(tester);
        IERC20DebtToken(address(pair.tokens(BORROW_X))).approveDebt(address(callee), borrowX);
        IERC20DebtToken(address(pair.tokens(BORROW_Y))).approveDebt(address(callee), borrowY);
        pair.borrow(address(callee), borrowX, borrowY, abi.encode(address(0)));
        vm.stopPrank();

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: initialX + amountX - 2 * borrowX,
                tokenY: initialY + amountY - 2 * borrowY,
                reserveXAssets: initialX + amountX,
                reserveYAssets: initialY + amountY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory testerState;

        testerState.toCheck = tester;
        testerState.balanceX = 2 * borrowX;
        testerState.balanceY = 2 * borrowY;
        testerState.pairLiquidity = 2e18;
        testerState.pairBorrowedX = 2 * borrowX;
        testerState.pairBorrowedY = 2 * borrowY;

        fixture.verifyAddress(testerState);
    }

    function reentryBorrowAgainCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory data
    ) external {
        address dataAddress = abi.decode(data, (address));
        // expect zero address on first call
        if (dataAddress == address(0)) {
            SafeERC20.safeTransfer(_fixture.tokenX(), sender, amountX);
            SafeERC20.safeTransfer(_fixture.tokenY(), sender, amountY);
            // encode sender to trigger else condition and to send back to original sender.
            _fixture.pair().borrow(address(this), amountX, amountY, abi.encode(sender));
        } else {
            // second call logic
            SafeERC20.safeTransfer(_fixture.tokenX(), dataAddress, amountX);
            SafeERC20.safeTransfer(_fixture.tokenY(), dataAddress, amountY);
            _fixture.pair().tokens(BORROW_X).safeTransfer(dataAddress, amountX);
            _fixture.pair().tokens(BORROW_Y).safeTransfer(dataAddress, amountY);
        }
    }

    function testFlashBorrowReentryDoubleBorrowTriesToRepayCallback() public {
        ICallback callee = new ReentryDelegatedCall(fixture, this.reentryBorrowCallback);

        vm.startPrank(tester);
        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        pair.borrow(address(callee), 0.75e18, 0.75e18, abi.encode(address(callee)));
    }

    function testFlashBorrowReentryDoubleBorrowFailsTriesToRepaySender() public {
        ICallback callee = new ReentryDelegatedCall(fixture, this.reentryBorrowCallback);

        vm.startPrank(tester);
        vm.expectRevert(
            bytes(
                string.concat(
                    'delegatecall Unknown(',
                    toHexCapitalString(Validation.AmmalgamDepositIsNotStrictlyBigger.selector),
                    ')'
                )
            )
        );
        pair.borrow(address(callee), 0.75e18, 0.75e18, abi.encode(address(tester)));
    }

    function reentryBorrowCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory data
    ) external {
        address repay = abi.decode(data, (address));
        IPairHarness _pair = _fixture.pair();
        if (
            // assumes tester started with 0 and on the second call sender is this contract.
            _fixture.tokenX().balanceOf(sender) == 0 && _fixture.tokenY().balanceOf(sender) == 0
        ) {
            SafeERC20.safeTransfer(_fixture.tokenX(), sender, amountX);
            SafeERC20.safeTransfer(_fixture.tokenY(), sender, amountY);
            _pair.borrow(address(this), amountX, amountY, abi.encode(repay));
        } else {
            SafeERC20.safeTransfer(_fixture.tokenX(), address(_pair), amountX);
            SafeERC20.safeTransfer(_fixture.tokenY(), address(_pair), amountY);
            _pair.repay(repay);
        }
    }

    function testFlashBorrowReentryMint() public {
        uint256 collateralMintX = 0.01e18;
        uint256 collateralMintY = 0.04e18;
        uint256 borrowMintX = 0.99e18; // max leveraged borrow is 100x
        uint256 borrowMintY = 3.96e18;

        uint256 originSqrtLiquidity = Math.sqrt((collateralMintX + borrowMintX) * (collateralMintY + borrowMintY));

        ICallback callee = new ReentryDelegatedCall(fixture, this.reentryMintCallback);

        (uint256 borrowMintXAdj, uint256 borrowMintYAdj) =
            adjustAmountsByTickPriceForBorrow(borrowMintX, borrowMintY, initialX, initialY);

        collateralMintX += borrowMintX - borrowMintXAdj;
        collateralMintY += borrowMintY - borrowMintYAdj;

        borrowMintX = borrowMintXAdj;
        borrowMintY = borrowMintYAdj;

        fixture.transferTokensTo(tester, collateralMintX, collateralMintY);

        vm.startPrank(tester);
        fixture.tokenX().approve(address(callee), collateralMintX);
        fixture.tokenY().approve(address(callee), collateralMintY);
        pair.borrow(address(callee), borrowMintX, borrowMintY, abi.encode(collateralMintX, collateralMintY));
        vm.stopPrank();

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: initialX + collateralMintX,
                tokenY: initialY + collateralMintY,
                reserveXAssets: initialX + collateralMintX + borrowMintX,
                reserveYAssets: initialY + collateralMintY + borrowMintY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory testerState;
        testerState.toCheck = tester;
        testerState.pairLiquidity = originSqrtLiquidity;
        testerState.pairBorrowedX = borrowMintX;
        testerState.pairBorrowedY = borrowMintY;

        fixture.verifyAddress(testerState);
    }

    /**
     * @dev  borrow first and mint the borrowed X and Y in the borrow callback.
     *
     * the number to be adjusted for borrowX and borrowY affects the netDepositedL to compare with netBorrowedInL.
     * netBorrowedInL = netBorrowedXinL + netBorrowedYinL
     * netBorrowedXinL = borrowedX * sprtPriceByTick  <- here is the number need to be adjusted for borrowX and borrowY but..
     * the adjustment affect the mint depositL
     *
     */
    function testFlashBorrowReentryToReceiverMintFails() public {
        ICallback callee = new ReentryDelegatedCall(fixture, this.reentryMintCallbackWithAdjustment);

        uint256 borrowMintX = 0.2e18;
        uint256 borrowMintY = 0.8e18;

        (uint256 borrowMintXAdj, uint256 borrowMintYAdj) =
            adjustAmountsByTickPrice(borrowMintX, borrowMintY, initialX, initialY);

        uint256 collateralMintXAdj = borrowMintX - borrowMintXAdj;
        uint256 collateralMintYAdj = borrowMintYAdj - borrowMintY;

        borrowMintX = borrowMintXAdj;
        borrowMintY = borrowMintYAdj;

        fixture.transferTokensTo(address(callee), collateralMintXAdj, 0);

        vm.startPrank(tester);

        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        pair.borrow(address(callee), borrowMintX, borrowMintY, abi.encode(collateralMintXAdj, collateralMintYAdj));
    }

    /**
     * @dev This function is specifically for testFlashBorrowReentryToReceiverMintFails.
     *
     * the amountX and amountY to mint need to be adjusted by the mintXAdj and mintYAdj because the borrowedX and borrowedY
     * will be adjusted in the validationSolvency with sqrtPriceX96 at tick after the callback.
     *
     */
    function reentryMintCallbackWithAdjustment(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory data
    ) external {
        address _pairAddress = _fixture.pairAddress();
        IPairHarness _pair = _fixture.pair();

        (uint256 mintXAdj, uint256 mintYAdj) = abi.decode(data, (uint256, uint256));

        SafeERC20.safeTransfer(_fixture.tokenX(), _pairAddress, amountX + mintXAdj);
        SafeERC20.safeTransfer(_fixture.tokenY(), _pairAddress, amountY - mintYAdj);
        _pair.mint(sender);
    }

    function reentryMintCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory data
    ) external {
        address _pairAddress = _fixture.pairAddress();
        IPairHarness _pair = _fixture.pair();
        (uint256 mintX, uint256 mintY) = abi.decode(data, (uint256, uint256));
        if (mintX > 0) {
            SafeERC20.safeTransferFrom(_fixture.tokenX(), sender, _pairAddress, mintX);
        }
        if (mintY > 0) {
            SafeERC20.safeTransferFrom(_fixture.tokenY(), sender, _pairAddress, mintY);
        }
        SafeERC20.safeTransfer(_fixture.tokenX(), _pairAddress, amountX);
        SafeERC20.safeTransfer(_fixture.tokenY(), _pairAddress, amountY);
        _pair.mint(sender);
    }

    function testFlashBorrowReentryBurnFails() public {
        uint256 mintX = 1e18;
        uint256 mintY = 4e18;
        uint256 burnL = 2e18;
        fixture.transferTokensTo(tester, mintX, mintY);
        fixture.mintFor(tester, mintX, mintY);

        ICallback callee = new ReentryDelegatedCall(fixture, this.reentryBurnCallback);

        vm.startPrank(tester);
        pair.tokens(DEPOSIT_L).approve(address(callee), burnL);
        vm.expectRevert(
            bytes(
                string.concat(
                    'delegatecall Unknown(',
                    toHexCapitalString(Validation.AmmalgamDepositIsNotStrictlyBigger.selector),
                    ')'
                )
            )
        );
        pair.borrow(address(callee), mintX, mintY, abi.encode(burnL));
    }

    function reentryBurnCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory data
    ) external {
        IPairHarness _pair = _fixture.pair();
        SafeERC20.safeTransferFrom(_pair.tokens(DEPOSIT_L), sender, address(_pair), abi.decode(data, (uint256)));
        (uint256 burnAmountX, uint256 burnAmountY) = _pair.burn(sender);
        // contract has borrow and burn amount, but will fail without repay or deposit
        SafeERC20.safeTransfer(_fixture.tokenX(), sender, amountX + burnAmountX);
        SafeERC20.safeTransfer(_fixture.tokenY(), sender, amountY + burnAmountY);
    }

    function testFlashBorrowReentrySwapFails() public {
        ICallback callee = new ReentryDelegatedCall(fixture, this.reentrySwapCallback);

        vm.startPrank(tester);
        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        pair.borrow(address(callee), 1e18, 0, '0x1');
    }

    function reentrySwapCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory /* data */
    ) external {
        IPairHarness _pair = _fixture.pair();

        if (amountX > 0) {
            SafeERC20.safeTransfer(_fixture.tokenX(), address(_pair), amountX);
            (uint256 reserveX, uint256 reserveY,) = _pair.getReserves();
            (uint256 referenceReserveX,) = _pair.referenceReserves();
            uint256 expectedOutSwap = computeExpectedSwapOutAmount(amountX, reserveX, referenceReserveX, reserveY, 0, 0);
            _pair.swap(0, expectedOutSwap, sender, '');
        } else {
            SafeERC20.safeTransfer(_fixture.tokenY(), address(_pair), amountY);
            (uint256 reserveX, uint256 reserveY,) = _pair.getReserves();
            (, uint256 referenceReserveY) = _pair.referenceReserves();
            uint256 expectedOutSwap = computeExpectedSwapOutAmount(amountY, reserveY, referenceReserveY, reserveX, 0, 0);
            _pair.swap(expectedOutSwap, 0, sender, '');
        }
    }

    function testFlashBorrowReentrySwapAndDeposit() public {
        uint256 collateralX = 1e18;
        uint256 borrowY = 1e18;
        uint256 expectedOut = computeExpectedSwapOutAmount(borrowY, initialY, initialX);
        ICallback callee = new ReentryDelegatedCall(fixture, this.reentrySwapYToXAndDeposit);

        fixture.transferTokensTo(tester, collateralX, 0);

        vm.startPrank(tester);
        fixture.tokenX().approve(address(callee), collateralX);
        pair.borrow(address(callee), 0, borrowY, abi.encode(collateralX, expectedOut));

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: initialX + collateralX,
                tokenY: initialY,
                reserveXAssets: initialX - expectedOut,
                reserveYAssets: initialY + borrowY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory testerState;

        testerState.toCheck = tester;
        testerState.pairDepositedX = collateralX + expectedOut;
        testerState.pairBorrowedY = borrowY;
    }

    function reentrySwapYToXAndDeposit(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256, /* amountX */
        uint256 amountY,
        uint256, /* amountL */
        bytes memory data
    ) external {
        (uint256 collateralX, uint256 expectedOutSwap) = abi.decode(data, (uint256, uint256));
        SafeERC20.safeTransfer(_fixture.tokenY(), address(_fixture.pair()), amountY);
        _fixture.pair().swap(expectedOutSwap, 0, address(this), '');
        SafeERC20.safeTransferFrom(_fixture.tokenX(), sender, address(_fixture.pair()), collateralX);
        SafeERC20.safeTransfer(_fixture.tokenX(), address(_fixture.pair()), expectedOutSwap);
        _fixture.pair().deposit(sender);
    }

    function testFlashBorrowReentryDepositFails() public {
        ICallback callee = new ReentryDelegatedCall(fixture, this.reentryDepositCallback);

        vm.startPrank(tester);
        vm.expectRevert(Validation.AmmalgamLTV.selector);
        pair.borrow(address(callee), 1e18, 0, '0x1');
    }

    function reentryDepositCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory /* data */
    ) external {
        IERC20 tokenX = _fixture.tokenX();
        IERC20 tokenY = _fixture.tokenY();
        IPairHarness _pair = _fixture.pair();
        address _pairAddress = address(_pair);
        if (amountX > 0) {
            SafeERC20.safeTransfer(tokenX, _pairAddress, amountX);
            (uint256 reserveX, uint256 reserveY,) = _pair.getReserves();
            (uint256 referenceReserveX,) = _pair.referenceReserves();
            uint256 expectedOutSwap = computeExpectedSwapOutAmount(amountX, reserveX, referenceReserveX, reserveY, 0, 0);
            _pair.swap(0, expectedOutSwap, address(this), '');
            SafeERC20.safeTransfer(tokenY, _pairAddress, amountY + expectedOutSwap);
            _pair.deposit(sender);
        } else {
            SafeERC20.safeTransfer(tokenY, _pairAddress, amountY);
            (uint256 reserveX, uint256 reserveY,) = _pair.getReserves();
            (, uint256 referenceReserveY) = _pair.referenceReserves();
            uint256 expectedOutSwap = computeExpectedSwapOutAmount(amountY, reserveY, referenceReserveY, reserveX, 0, 0);
            _pair.swap(expectedOutSwap, 0, address(this), '');
            SafeERC20.safeTransfer(tokenX, _pairAddress, amountX + expectedOutSwap);
            _pair.deposit(sender);
        }
    }

    function testFlashBorrowReentryWithdrawXFails() public {
        uint256 reentryWithdrawDepositedX = 1e18;
        uint256 reentryWithdrawDepositedY = 0;
        fixture.transferTokensTo(tester, reentryWithdrawDepositedX, reentryWithdrawDepositedY);
        fixture.depositFor(tester, reentryWithdrawDepositedX, reentryWithdrawDepositedY);

        ICallback callee = new ReentryDelegatedCall(fixture, this.reentryWithdrawCallback);

        vm.startPrank(tester);
        pair.tokens(DEPOSIT_X).approve(address(callee), reentryWithdrawDepositedX);
        vm.expectRevert(
            bytes(
                string.concat(
                    'delegatecall Unknown(',
                    toHexCapitalString(Validation.AmmalgamDepositIsNotStrictlyBigger.selector),
                    ')'
                )
            )
        );
        pair.borrow(address(callee), 0, 4e18, abi.encode(reentryWithdrawDepositedX, reentryWithdrawDepositedY));
    }

    function testFlashBorrowReentryWithdrawYFails() public {
        uint256 reentryWithdrawDepositedX = 0;
        uint256 reentryWithdrawDepositedY = 4e18;
        fixture.transferTokensTo(tester, reentryWithdrawDepositedX, reentryWithdrawDepositedY);
        fixture.depositFor(tester, reentryWithdrawDepositedX, reentryWithdrawDepositedY);

        ICallback callee = new ReentryDelegatedCall(fixture, this.reentryWithdrawCallback);

        vm.startPrank(tester);
        pair.tokens(DEPOSIT_Y).approve(address(callee), reentryWithdrawDepositedY);
        vm.expectRevert(
            bytes(
                string.concat(
                    'delegatecall Unknown(',
                    toHexCapitalString(Validation.AmmalgamDepositIsNotStrictlyBigger.selector),
                    ')'
                )
            )
        );
        pair.borrow(address(callee), 1e18, 0, abi.encode(reentryWithdrawDepositedX, reentryWithdrawDepositedY));
    }

    function reentryWithdrawCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory data
    ) public {
        IPairHarness _pair = _fixture.pair();
        SafeERC20.safeTransfer(_fixture.tokenX(), sender, amountX);
        SafeERC20.safeTransfer(_fixture.tokenY(), sender, amountY);
        (uint256 depositedX, uint256 depositedY) = abi.decode(data, (uint256, uint256));
        if (depositedX > 0) {
            SafeERC20.safeTransferFrom(_pair.tokens(DEPOSIT_X), sender, address(_pair), depositedX);
            SafeERC20.safeTransfer(_fixture.tokenX(), sender, depositedX);
        }
        if (depositedY > 0) {
            SafeERC20.safeTransferFrom(_pair.tokens(DEPOSIT_Y), sender, address(_pair), depositedY);
            SafeERC20.safeTransfer(_fixture.tokenY(), sender, depositedY);
        }
        _pair.withdraw(sender);
    }

    function testFlashBorrowFlashBorrowReentryLiquidityToCalleeFails() public {
        ICallback callee = new ReentryDelegatedCall(fixture, this.reentryBorrowLiquidityCallback);
        vm.startPrank(tester);
        vm.expectRevert(
            bytes(
                string.concat(
                    'delegatecall Unknown(',
                    toHexCapitalString(Validation.AmmalgamDepositIsNotStrictlyBigger.selector),
                    ')'
                )
            )
        );
        pair.borrow(address(callee), 0.1e18, 0.4e18, abi.encode(callee));
    }

    function testFlashBorrowReentryLiquidityToTesterFails() public {
        ICallback callee = new ReentryDelegatedCall(fixture, this.reentryBorrowLiquidityCallback);

        vm.startPrank(tester);
        vm.expectRevert(
            bytes(
                string.concat(
                    'delegatecall Unknown(',
                    toHexCapitalString(Validation.AmmalgamDepositIsNotStrictlyBigger.selector),
                    ')'
                )
            )
        );
        pair.borrow(address(callee), 0.1e18, 0.4e18, abi.encode(tester));
    }

    function reentryBorrowLiquidityCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory data
    ) public {
        address repay = abi.decode(data, (address));
        SafeERC20.safeTransfer(_fixture.tokenX(), sender, amountX);
        SafeERC20.safeTransfer(_fixture.tokenY(), sender, amountY);
        _fixture.pair().borrowLiquidity(sender, Math.sqrt(amountX * amountY), '');

        SafeERC20.safeTransfer(_fixture.tokenX(), repay, amountX);
        SafeERC20.safeTransfer(_fixture.tokenY(), repay, amountY);
    }

    function testFlashBorrowReentryRepayLiquidityFails() public {
        fixture.transferTokensTo(tester, 1.5e18, 6e18);
        fixture.mintFor(tester, 1.5e18, 6e18);
        fixture.borrowLiquidityFor(tester, 2e18);

        ICallback callee = new ReentryDelegatedCall(fixture, this.reentryRepayLiquidityCallback);

        address tempTester = vm.addr(101);
        vm.startPrank(tempTester);
        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        pair.borrow(address(callee), 1e18, 4e18, abi.encode(tester));
    }

    function reentryRepayLiquidityCallback(
        FactoryPairTestFixture _fixture,
        address, /* sender */
        uint256 amountX,
        uint256 amountY,
        uint256, /* amountL */
        bytes memory data
    ) public {
        address debtOwner = abi.decode(data, (address));
        SafeERC20.safeTransfer(_fixture.tokenX(), _fixture.pairAddress(), amountX);
        SafeERC20.safeTransfer(_fixture.tokenY(), _fixture.pairAddress(), amountY);
        _fixture.pair().repayLiquidity(debtOwner);
    }

    function testFlashBorrowReentrySkimFails() public {
        ICallback callee = new ReentryDelegatedCall(fixture, this.reentrySkimCallback);

        vm.startPrank(tester);
        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        pair.borrow(address(callee), 1e18, 4e18, '0x1');
    }

    function reentrySkimCallback(
        FactoryPairTestFixture _fixture,
        address sender,
        uint256, /* amountX */
        uint256, /* amountY */
        uint256, /* amountL */
        bytes memory /* data */
    ) public {
        _fixture.pair().skim(sender);
    }

    function testFlashBorrowReentrySyncFails() public {
        ICallback callee = new ReentryDelegatedCall(fixture, this.reentrySyncCallback);

        vm.startPrank(tester);
        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        pair.borrow(address(callee), 1e18, 4e18, '0x1');
    }

    function reentrySyncCallback(
        FactoryPairTestFixture _fixture,
        address, /* sender */
        uint256, /* amountX */
        uint256, /* amountY */
        uint256, /* amountL */
        bytes memory /* data */
    ) public {
        _fixture.pair().sync();
    }

    function getReentryDelegatedCall(
        function(FactoryPairTestFixture, address, uint256, uint256, uint256, bytes memory) external delegatedCall
    ) public returns (ICallback) {
        return new ReentryDelegatedCall(fixture, delegatedCall);
    }
}

contract ReentryDelegatedCall is ICallback {
    FactoryPairTestFixture fixture;
    function(FactoryPairTestFixture, address, uint256, uint256, uint256, bytes memory) external private delegatedCall;

    constructor(
        FactoryPairTestFixture _fixture,
        function(FactoryPairTestFixture, address, uint256, uint256, uint256, bytes memory) external delegatedCall_
    ) {
        fixture = _fixture;
        delegatedCall = delegatedCall_;
    }

    function ammalgamSwapCallV1(address sender, uint256 amountX, uint256 amountY, bytes calldata data) external {
        /* noop */
    }

    function ammalgamBorrowCallV1(
        address sender,
        uint256 amountXAssets,
        uint256 amountYAssets,
        uint256, // amountXShares,
        uint256, // amountYShares,
        bytes calldata data
    ) external {
        address delegatedAddress = delegatedCall.address;
        (bool success, bytes memory message) = delegatedAddress.delegatecall(
            abi.encodeWithSelector(delegatedCall.selector, fixture, sender, amountXAssets, amountYAssets, 0, data)
        );
        require(success, RevertReasonParser.parse(message, 'delegatecall '));
    }

    function ammalgamBorrowLiquidityCallV1(
        address sender,
        uint256 amountXAssets,
        uint256 amountYAssets,
        uint256 amountLShares,
        bytes calldata data
    ) public {
        address delegatedAddress = delegatedCall.address;
        (bool success, bytes memory message) = delegatedAddress.delegatecall(
            abi.encodeWithSelector(
                delegatedCall.selector, fixture, sender, amountXAssets, amountYAssets, amountLShares, data
            )
        );
        require(success, RevertReasonParser.parse(message, 'delegatecall '));
    }

    function ammalgamLiquidateCallV1(uint256 repayXInXAssets, uint256 repayYInYAssets) external { /* noop */ }
}
