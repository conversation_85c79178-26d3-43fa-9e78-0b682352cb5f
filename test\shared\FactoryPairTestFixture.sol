// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Vm} from 'forge-std/Vm.sol';
import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';
import {IERC4626} from '@openzeppelin/contracts/interfaces/IERC4626.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {AmmalgamPair} from 'contracts/AmmalgamPair.sol';
import {AmmalgamFactory} from 'contracts/factories/AmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {IAmmalgamFactory, IPairFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IERC20DebtToken} from 'contracts/interfaces/tokens/IERC20DebtToken.sol';
import {GeometricTWAP} from 'contracts/libraries/GeometricTWAP.sol';
import {QuadraticSwapFees} from 'contracts/libraries/QuadraticSwapFees.sol';
import {DepletedAssetUtils} from 'test/utils/DepletedAssetUtils.sol';
import {deployFactory} from 'contracts/utils/deployHelper.sol';
import {ISaturationAndGeometricTWAPState} from 'contracts/interfaces/ISaturationAndGeometricTWAPState.sol';
import {SaturationAndGeometricTWAPState} from 'contracts/SaturationAndGeometricTWAPState.sol';
import {Saturation} from 'contracts/libraries/Saturation.sol';
import {
    BORROW_L,
    BORROW_X,
    BORROW_Y,
    DEPOSIT_L,
    DEPOSIT_X,
    DEPOSIT_Y,
    FIRST_DEBT_TOKEN,
    ROUNDING_UP
} from 'contracts/interfaces/tokens/ITokenController.sol';
import {ITokenController} from 'contracts/interfaces/tokens/ITokenController.sol';
import {DEFAULT_MID_TERM_INTERVAL} from 'contracts/libraries/constants.sol';
import {Convert} from 'contracts/libraries/Convert.sol';
import {RAY} from 'test/InterestTests/InterestFixture.sol';
import {Validation} from 'contracts/libraries/Validation.sol';
import {
    IDebtDelegation, PeripheralDelegationContractExample
} from 'test/example/PeripheralDelegationContractExample.sol';
import {computeExpectedSwapInAmount, computeExpectedSwapOutAmount, getStubToken} from 'test/shared/utilities.sol';
import {DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG} from 'contracts/libraries/constants.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';
import {Q128} from 'contracts/libraries/constants.sol';

uint256 constant MAX_TOKEN = uint256(type(uint112).max) ** 2;
uint112 constant MAXIMUM_AMM_RESERVE = type(uint112).max;

interface IPairHarness is IAmmalgamPair {
    // function exposed_getInputParams(address a, bool b) external view returns (Validation.InputParams memory inputParams, bool hasBorrow);

    /**
     * @notice Retrieves the current observations from the contract.
     * @dev Returns the GeometricTWAP Observation struct.
     * @return observations A struct containing the current observations data.
     */
    function exposed_observations() external view returns (GeometricTWAP.Observations memory);

    /**
     * @notice Retrieves the tick range values.
     * @dev Returns the minimum and maximum tick values that define a tick range.
     * @param currentTick The current (most recent) tick based on the current reserves.
     * @return minTick The minimum tick value among current, mid-term, and long-term ticks.
     * @return maxTick The maximum tick value among current, mid-term, and long-term ticks.
     */
    function exposed_getTickRange(
        int16 currentTick
    ) external view returns (int16 minTick, int16 maxTick);
    function exposed_getTickRange() external view returns (int16 minTick, int16 maxTick);
    function exposed_getTickRangeWithoutLongTerm(
        int16 currentTick
    ) external view returns (int16, int16);
    function exposed_getTickRangeWithoutLongTerm() external view returns (int16, int16);

    function exposed_externalLiquidity() external view returns (uint112);

    function exposed_sharesToAssetsScaler(
        uint256 tokenType
    ) external view returns (uint256);

    function exposed_getDepositAndBorrowAndActiveLiquidityAssets() external view returns (uint256, uint256, uint256);

    function exposed_getLendingStateTick(
        int56 lastLendingCumulativeSum,
        uint256 duration
    ) external view returns (int16);

    /**
     * @notice Represents the amount of liquidity assets available in the
     *         contract.
     */
    function exposed_activeLiquidityAssets() external view returns (uint256);

    function exposed_setReferenceReserves(uint256 referenceReserveX, uint256 referenceReserveY) external;

    function exposed_getAssets(
        address user
    ) external view returns (uint256[6] memory);

    function exposed_getAssets(uint256 tokenType, address user) external view returns (uint256);

    function exposed_missingAssets() external view returns (uint112, uint112);
    // function exposed_getReserves() external view returns (uint112, uint112);

    function exposed_totalSat() external view returns (uint256 totalSatNetX, uint256 totalSatNetY);

    function exposed_resetTotalAssetsCached() external;

    function exposed_getTotalAssetsCached(
        uint256 tokenType
    ) external returns (uint256);

    function exposed_accrueInterestToToken(uint256 tokenType, uint128 amount) external;

    function exposed_saturationAndGeometricTWAPState() external view returns (ISaturationAndGeometricTWAPState);
}

contract PairHarness is IPairHarness, AmmalgamPair, Test {
    function exposed_observations() external view returns (GeometricTWAP.Observations memory) {
        return saturationAndGeometricTWAPState.getObservations(address(this));
    }

    function getCurrentTick() private view returns (int16 currentTick) {
        (uint256 _reserveXAssets, uint256 _reserveYAssets,) = getReserves();
        uint256 priceInQ128 = (_reserveXAssets * Q128) / _reserveYAssets;
        currentTick = TickMath.getTickAtPrice(priceInQ128);
    }

    function exposed_getTickRange(
        int16 currentTick
    ) external view returns (int16, int16) {
        // slither-disable-next-line unused-return false complaint it returns minTick, maxTick
        return saturationAndGeometricTWAPState.getTickRange(address(this), currentTick, true);
    }

    function exposed_getTickRange() external view returns (int16, int16) {
        return this.exposed_getTickRange(getCurrentTick());
    }

    function exposed_getTickRangeWithoutLongTerm(
        int16 currentTick
    ) external view returns (int16, int16) {
        // slither-disable-next-line unused-return false complaint it returns minTick, maxTick
        return saturationAndGeometricTWAPState.getTickRange(address(this), currentTick, false);
    }

    function exposed_getTickRangeWithoutLongTerm() external view returns (int16, int16) {
        return this.exposed_getTickRangeWithoutLongTerm(getCurrentTick());
    }

    function exposed_externalLiquidity() external view returns (uint112) {
        return externalLiquidity;
    }

    function exposed_setReferenceReserves(uint256 _referenceReserveX, uint256 _referenceReserveY) external {
        referenceReserveX = uint112(_referenceReserveX);
        referenceReserveY = uint112(_referenceReserveY);
    }

    function exposed_sharesToAssetsScaler(
        uint256 tokenType
    ) external view returns (uint256) {
        uint256 _totalShares = totalShares(tokenType);
        return _totalShares == 0 ? RAY : Math.mulDiv(totalAssets()[tokenType], RAY, _totalShares, Math.Rounding.Ceil);
    }

    function exposed_getDepositAndBorrowAndActiveLiquidityAssets() external view returns (uint256, uint256, uint256) {
        return getDepositAndBorrowAndActiveLiquidityAssets();
    }

    function exposed_getLendingStateTick(
        int56 lastLendingCumulativeSum,
        uint256 duration
    ) external view returns (int16 lendingStateTick) {
        uint256 lastIndex = GeometricTWAP.MID_TERM_ARRAY_LENGTH - 1;
        uint256 index = saturationAndGeometricTWAPState.getObservations(address(this)).midTermIndex == 0
            ? lastIndex
            : saturationAndGeometricTWAPState.getObservations(address(this)).midTermIndex - 1;

        int56 currentCumulativeSum =
            int56(saturationAndGeometricTWAPState.getObservations(address(this)).midTermCumulativeSum[index]);

        lendingStateTick = int16((currentCumulativeSum - lastLendingCumulativeSum) / int256(duration));
    }

    function exposed_activeLiquidityAssets() public view override returns (uint256) {
        return rawTotalAssets(DEPOSIT_L) - rawTotalAssets(BORROW_L);
    }

    function exposed_getAssets(
        address user
    ) external view returns (uint256[6] memory) {
        return getAssets(totalAssets(), user);
    }

    function exposed_getAssets(uint256 tokenType, address user) public view returns (uint256) {
        return getAssets(totalAssets(), user)[tokenType];
    }

    function exposed_missingAssets() public view returns (uint112, uint112) {
        return missingAssets();
    }

    // saturation

    function exposed_totalSat() external view returns (uint256 totalSatNetX, uint256 totalSatNetY) {
        (, totalSatNetX) = saturationAndGeometricTWAPState.getTreeDetails(address(this), true);
        (, totalSatNetY) = saturationAndGeometricTWAPState.getTreeDetails(address(this), false);
    }

    function exposed_resetTotalAssetsCached() public {
        totalDepositLAssets = 0;
        totalDepositXAssets = 0;
        totalDepositYAssets = 0;
        totalBorrowLAssets = 0;
        totalBorrowXAssets = 0;
        totalBorrowYAssets = 0;
    }

    function exposed_getTotalAssetsCached(
        uint256 tokenType
    ) public view returns (uint256 tokenTotalAssets) {
        if (tokenType == DEPOSIT_L) {
            tokenTotalAssets = totalDepositLAssets;
        } else if (tokenType == DEPOSIT_X) {
            tokenTotalAssets = totalDepositXAssets;
        } else if (tokenType == DEPOSIT_Y) {
            tokenTotalAssets = totalDepositYAssets;
        } else if (tokenType == BORROW_L) {
            tokenTotalAssets = totalBorrowLAssets;
        } else if (tokenType == BORROW_X) {
            tokenTotalAssets = totalBorrowXAssets;
        } else if (tokenType == BORROW_Y) {
            tokenTotalAssets = totalBorrowYAssets;
        } else {
            require(false, 'Unsupported TokenType');
        }
    }

    function exposed_accrueInterestToToken(uint256 tokenType, uint128 amount) public override {
        require(tokenType < FIRST_DEBT_TOKEN);
        allAssets[tokenType] += amount;
        allAssets[tokenType + FIRST_DEBT_TOKEN] += amount;
    }

    function exposed_saturationAndGeometricTWAPState() external view returns (ISaturationAndGeometricTWAPState) {
        return saturationAndGeometricTWAPState;
    }
}

/**
 * @title PairHarnessFactory
 * @notice Implementation of the for the IPairFactory interface using the
 *         PairHarness for testing.
 */
contract PairHarnessFactory is IPairFactory, Test {
    function createPair(
        bytes32 salt
    ) external returns (address pair) {
        bytes memory bytecode = type(PairHarness).creationCode;
        // slither-disable-start assembly
        assembly {
            pair := create2(0, add(bytecode, 32), mload(bytecode), salt)
        }
        // slither-disable-end assembly
    }
}

/**
 * @notice Test to ignore build size.
 */
contract StubSaturationAndGeometricTWAPState is SaturationAndGeometricTWAPState, Test {
    bool stubSaturation;
    bool stubTWAP;
    int16 _tickMin;
    int16 _tickMax;
    bool tickRangeSet;

    constructor(
        bool _stubSaturation,
        bool _stubTWAP
    ) SaturationAndGeometricTWAPState(DEFAULT_MID_TERM_INTERVAL, MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG) {
        stubSaturation = _stubSaturation;
        stubTWAP = _stubTWAP;
    }

    // Saturation
    function update(Validation.InputParams memory inputParams, address account) public override {
        if (!stubSaturation) {
            return _update(inputParams, account);
        }
        // noop, if `stubSaturation` is `true`
    }

    // TWAP
    function setTickRange(int16 tickMin, int16 tickMax) external {
        require(stubTWAP, 'StubSaturationAndGeometricTWAPState: setTickRange can only be called when stubTWAP is true');

        tickRangeSet = true;
        _tickMin = tickMin;
        _tickMax = tickMax;
    }

    function getTickRange(
        address pair,
        int16 currentTick,
        bool includeLongTermTick
    ) public view override returns (int16, int16) {
        if (stubTWAP) {
            if (tickRangeSet) {
                return (_tickMin, _tickMax);
            } else if (currentTick != 0) {
                return (currentTick, currentTick + 1);
            }
        }
        (int16 minTick, int16 maxTick) = _getTickRange(pair, currentTick, includeLongTermTick);
        return (minTick, maxTick);
    }

    function calcSatChangeRatioBips(
        Validation.InputParams memory inputParams,
        uint256 liqSqrtPriceInXInQ72,
        uint256 liqSqrtPriceInYInQ72,
        address pairAddress,
        address account
    ) external view virtual override returns (uint256 ratioNetXBips, uint256 ratioNetYBips) {
        if (!stubSaturation) {
            Saturation.SaturationStruct storage satStruct = satDataGivenPair[pairAddress];
            uint256 desiredSaturationInMAG2 = getNewPositionSaturation(pairAddress, account);

            return Saturation.calcSatChangeRatioBips(
                satStruct, inputParams, liqSqrtPriceInXInQ72, liqSqrtPriceInYInQ72, account, desiredSaturationInMAG2
            );
        }
        // noop, return (0, 0) if stubSaturation is true
    }
}

/**
 * Test Fixture to allow for simple creation of test scenarios and standardized verifications.
 */
contract FactoryPairTestFixture is Test {
    IERC20 public immutable tokenX;
    IERC20 public immutable tokenY;

    IAmmalgamFactory public immutable factory;
    IPairHarness public immutable pair;
    address public immutable pairAddress;
    IDebtDelegation public peripheralDelegationContract;
    StubSaturationAndGeometricTWAPState public immutable saturationAndGeometricTWAPState;

    event Deposit(address indexed sender, address indexed owner, uint256 assets, uint256 shares);
    event Withdraw(
        address indexed sender, address indexed receiver, address indexed owner, uint256 assets, uint256 shares
    );
    event Borrow(address indexed sender, address indexed to, uint256 assets, uint256 shares);
    event BorrowLiquidity(address indexed sender, address indexed to, uint256 assets, uint256 shares);
    event Repay(address indexed sender, address indexed onBehalfOf, uint256 assets, uint256 shares);
    event RepayLiquidity(address indexed sender, address indexed onBehalfOf, uint256 assets, uint256 shares);

    constructor(uint256 toMintX, uint256 toMintY, bool stubSaturation, bool stubTWAP) {
        vm.setNonce(address(this), 2); // nonce changes hash of tokens so that address(x) != address(y) in factory.
        IERC20 _tokenX = getStubToken('StubERC20X', 'STUBX', toMintX);
        IERC20 _tokenY = getStubToken('StubERC20Y', 'STUBY', toMintY);

        //swap tokens address in order of its order in Ammalgam
        (tokenX, tokenY) = _tokenX < _tokenY ? (_tokenX, _tokenY) : (_tokenY, _tokenX);

        IPairFactory pairFactory = new PairHarnessFactory();
        saturationAndGeometricTWAPState = new StubSaturationAndGeometricTWAPState(stubSaturation, stubTWAP);

        factory = deployFactory(address(this), pairFactory, address(saturationAndGeometricTWAPState));

        pairAddress = factory.createPair(address(tokenX), address(tokenY));
        pair = IPairHarness(pairAddress);

        peripheralDelegationContract = new PeripheralDelegationContractExample();
    }

    function getVm() public pure returns (Vm) {
        return vm;
    }

    function updateExternalLiquidity(
        uint112 externalLiquidity
    ) public {
        pair.updateExternalLiquidity(externalLiquidity);
    }

    function configLongTermInterval(
        uint24 longTermIntervalConfig
    ) public {
        saturationAndGeometricTWAPState.configLongTermInterval(pairAddress, longTermIntervalConfig);
    }

    function transferTokensTo(address to, uint256 amountX, uint256 amountY) public returns (FactoryPairTestFixture) {
        tokenX.transfer(to, amountX);
        tokenY.transfer(to, amountY);

        pair.exposed_resetTotalAssetsCached();

        return this;
    }

    function mintForAndInitializeBlocks(
        address to,
        uint256 tokenXAmount,
        uint256 tokenYAmount
    ) public returns (uint256 addedLiquidity) {
        addedLiquidity = mintFor(to, tokenXAmount, tokenYAmount);
        initializeLongTermBufferWithBlocks();
    }

    function mintFor(address to, uint256 tokenXAmount, uint256 tokenYAmount) public returns (uint256 addedLiquidity) {
        vm.startPrank(to);
        transferTokensTo(address(pair), tokenXAmount, tokenYAmount);
        addedLiquidity = pair.mint(to);
        pair.exposed_resetTotalAssetsCached();

        vm.stopPrank();
    }

    function depositFor(address to, uint256 tokenXAmount, uint256 tokenYAmount) public {
        depositFor(to, tokenXAmount, tokenYAmount, 0, 0);
    }

    function depositFor(
        address to,
        uint256 tokenXAmount,
        uint256 tokenYAmount,
        uint256 expectedReserveFromX,
        uint256 expectedReserveFromY
    ) public {
        vm.startPrank(to);

        transferTokensTo(address(pair), tokenXAmount, tokenYAmount);

        if (tokenXAmount > 0) {
            vm.expectEmit(true, true, false, true);
            emit Deposit(
                to,
                to,
                tokenXAmount + expectedReserveFromX,
                convertToShares(DEPOSIT_X, tokenXAmount + expectedReserveFromX, !ROUNDING_UP)
            );
        }
        if (tokenYAmount > 0) {
            vm.expectEmit(true, true, false, true);
            emit Deposit(
                to,
                to,
                tokenYAmount + expectedReserveFromY,
                convertToShares(DEPOSIT_Y, tokenYAmount + expectedReserveFromY, !ROUNDING_UP)
            );
        }

        pair.deposit(to);
        pair.exposed_resetTotalAssetsCached();

        vm.stopPrank();
    }

    function convertToShares(uint256 tokenType, uint256 assets, bool roundingUp) public view returns (uint256) {
        uint256 totalShares = pair.tokens(tokenType).totalSupply();
        uint256 totalAssets = pair.totalAssets()[tokenType];

        return totalShares == 0 ? assets : Convert.toShares(assets, totalAssets, totalShares, roundingUp);
    }

    function convertToAssets(uint256 tokenType, uint256 shares) public view returns (uint256) {
        uint256 totalShares = pair.tokens(tokenType).totalSupply();
        uint256 totalAssets = pair.totalAssets()[tokenType];
        return totalAssets == 0 ? shares : shares * totalAssets / totalShares;
    }

    function repayFor(address to, uint256 repayAmountX, uint256 repayAmountY) public {
        repayFor(to, repayAmountX, repayAmountY, 0, 0);
    }

    function repayFor(
        address to,
        uint256 repayAmountX,
        uint256 repayAmountY,
        uint256 expectedReserveFromX,
        uint256 expectedReserveFromY
    ) public {
        vm.startPrank(to);

        transferTokensTo(address(pair), repayAmountX, repayAmountY);

        if (repayAmountX > 0) {
            vm.expectEmit(true, true, false, true);
            emit Repay(
                to,
                to,
                repayAmountX + expectedReserveFromX,
                convertToShares(BORROW_X, repayAmountX + expectedReserveFromX, ROUNDING_UP)
            );
        }
        if (repayAmountY > 0) {
            vm.expectEmit(true, true, false, true);
            emit Repay(
                to,
                to,
                repayAmountY + expectedReserveFromY,
                convertToShares(BORROW_Y, repayAmountY + expectedReserveFromY, ROUNDING_UP)
            );
        }

        pair.repay(to);
        pair.exposed_resetTotalAssetsCached();

        vm.stopPrank();
    }

    function repayForNoEvent(address to, uint256 repayAmountX, uint256 repayAmountY) public {
        vm.startPrank(to);
        transferTokensTo(address(pair), repayAmountX, repayAmountY);

        pair.repay(to);
        pair.exposed_resetTotalAssetsCached();

        vm.stopPrank();
    }

    function repayLiquidityFor(address to, uint256 repayLX, uint256 repayLY, uint256 repayL) public {
        vm.startPrank(to);

        transferTokensTo(address(pair), repayLX, repayLY);

        vm.expectEmit(true, true, false, false);
        emit RepayLiquidity(to, to, repayL, convertToShares(BORROW_L, repayL, ROUNDING_UP));

        pair.repayLiquidity(to);
        pair.exposed_resetTotalAssetsCached();

        vm.stopPrank();
    }

    function repayLiquidityForNoEvent(address to, uint256 repayLX, uint256 repayLY) public {
        vm.startPrank(to);
        transferTokensTo(address(pair), repayLX, repayLY);

        pair.repayLiquidity(to);
        pair.exposed_resetTotalAssetsCached();

        vm.stopPrank();
    }

    function approveDebt(
        address receiver,
        address spender,
        uint256 tokenType,
        uint256 amount
    ) public returns (IERC20DebtToken debtToken) {
        debtToken = IERC20DebtToken(address(pair.tokens(tokenType)));
        pair.exposed_resetTotalAssetsCached();

        vm.prank(receiver);
        debtToken.approveDebt(spender, amount);
        pair.exposed_resetTotalAssetsCached();
    }

    function borrowFor(address to, uint256 amountX, uint256 amountY) public {
        if (amountX > 0) {
            vm.expectEmit(true, true, false, true);
            emit Borrow(to, to, amountX, amountX);
        } else if (amountY > 0) {
            vm.expectEmit(true, true, false, true);
            emit Borrow(to, to, amountY, amountY);
        }
        vm.prank(to);
        pair.borrow(to, amountX, amountY, '');
        pair.exposed_resetTotalAssetsCached();
    }

    function borrowForNoEvent(address to, uint256 amountX, uint256 amountY) public {
        vm.prank(to);
        pair.borrow(to, amountX, amountY, '');
    }

    function borrowLiquidityFor(
        address _tester,
        uint256 borrowLAssets
    ) public returns (uint256 returnedLx, uint256 returnedLy) {
        vm.expectEmit(true, true, false, true);

        emit BorrowLiquidity(_tester, _tester, borrowLAssets, convertToShares(BORROW_L, borrowLAssets, ROUNDING_UP));
        vm.prank(_tester);

        (returnedLx, returnedLy) = pair.borrowLiquidity(_tester, borrowLAssets, '');
        pair.exposed_resetTotalAssetsCached();
    }

    function borrowLiquidityForNoEvent(
        address _tester,
        uint256 borrowL
    ) public returns (uint256 returnedLx, uint256 returnedLy) {
        vm.prank(_tester);
        (returnedLx, returnedLy) = pair.borrowLiquidity(_tester, borrowL, '');
    }

    function withdrawFor(address to, uint256 shareX, uint256 shareY) public {
        vm.startPrank(to);

        if (shareX > 0) {
            IERC20 depositX = pair.tokens(DEPOSIT_X);
            depositX.transfer(address(pair), shareX);
        }
        if (shareY > 0) {
            IERC20 depositY = pair.tokens(DEPOSIT_Y);
            depositY.transfer(address(pair), shareY);
        }

        if (shareX > 0) {
            uint256 assetX = convertToAssets(DEPOSIT_X, shareX);
            vm.expectEmit(true, true, true, true);
            emit Withdraw(address(pair), to, to, assetX, shareX);
        }
        if (shareY > 0) {
            uint256 assetY = convertToAssets(DEPOSIT_Y, shareY);
            vm.expectEmit(true, true, true, true);
            emit Withdraw(address(pair), to, to, assetY, shareY);
        }

        pair.withdraw(to);
        pair.exposed_resetTotalAssetsCached();

        vm.stopPrank();
    }

    function withdrawForNoEvent(address to, uint256 shareX, uint256 shareY) public {
        vm.startPrank(to);

        if (shareX > 0) {
            IERC20 depositX = pair.tokens(DEPOSIT_X);
            depositX.transfer(address(pair), shareX);
        }
        if (shareY > 0) {
            IERC20 depositY = pair.tokens(DEPOSIT_Y);
            depositY.transfer(address(pair), shareY);
        }

        pair.exposed_resetTotalAssetsCached();

        pair.withdraw(to);
        pair.exposed_resetTotalAssetsCached();

        vm.stopPrank();
    }

    function burnFor(address owner, uint256 amountL) public returns (uint256 amountX, uint256 amountY) {
        IERC20 liquidityToken = IERC20(pair.tokens(DEPOSIT_L));
        vm.prank(owner);
        liquidityToken.transfer(pairAddress, amountL);

        pair.exposed_resetTotalAssetsCached();
        (amountX, amountY) = pair.burn(owner);
        pair.exposed_resetTotalAssetsCached();
    }

    function factorySetFeeTo(
        address to
    ) public {
        factory.setFeeTo(to);
    }

    function verifySwapXToY(
        address user,
        uint256 swapInAmountX,
        uint256 tokenXAmount,
        uint256 tokenYAmount
    ) public returns (uint256 expected, uint112 reserveX, uint112 reserveY) {
        (expected, reserveX, reserveY) = verifySwapXToY(user, swapInAmountX, tokenXAmount, tokenYAmount, 0);
    }

    function verifySwapXToY(
        address user,
        uint256 swapInAmountX,
        uint256 tokenXAmount,
        uint256 tokenYAmount,
        uint256 missingYAssets
    ) public returns (uint256 expected, uint112 reserveX, uint112 reserveY) {
        (uint256 referenceReserveX,) = pair.referenceReserves();
        vm.startPrank(user);

        // swapXIn
        tokenX.transfer(address(pair), swapInAmountX);

        // off by one on expected amount in some cases.
        expected = computeExpectedSwapOutAmount(
            swapInAmountX, tokenXAmount, referenceReserveX, tokenYAmount, 0, missingYAssets
        );
        vm.assume(expected > 0); // zero out swap will fail.

        if (expected + 1 < tokenYAmount) {
            vm.expectRevert(AmmalgamPair.K.selector);
        } else {
            // swap will leave reserves at 0
            vm.expectRevert(AmmalgamPair.InsufficientLiquidity.selector);
        }

        pair.swap(0, expected + 1, user, '');
        // Don't expect revert
        pair.swap(0, expected, user, '');

        (reserveX, reserveY,) = pair.getReserves();
        assertEq(reserveX, swapInAmountX + tokenXAmount);
        assertEq(reserveY, tokenYAmount - expected);
        vm.stopPrank();
    }

    function verifySwapYToX(
        address user,
        uint256 swapAmountIn,
        uint256 reserveIn,
        uint256 reserveOut
    ) public returns (uint256 expected, uint112 reserveX, uint112 reserveY) {
        (expected, reserveX, reserveY) = verifySwapYToX(user, swapAmountIn, reserveIn, reserveOut, 0);
    }

    function verifySwapYToX(
        address user,
        uint256 swapAmountYIn,
        uint256 reserveYIn,
        uint256 reserveXOut,
        uint256 missingXAssets
    ) public returns (uint256 expected, uint112 reserveX, uint112 reserveY) {
        (, uint256 referenceReserveY) = pair.referenceReserves();
        vm.startPrank(user);

        // swapYIn
        tokenY.transfer(address(pair), swapAmountYIn);

        expected =
            computeExpectedSwapOutAmount(swapAmountYIn, reserveYIn, referenceReserveY, reserveXOut, 0, missingXAssets);
        if (expected + 1 < reserveXOut) {
            vm.expectRevert(AmmalgamPair.K.selector);
        } else {
            // swap will leave reserves at 0
            vm.expectRevert(AmmalgamPair.InsufficientLiquidity.selector);
        }

        pair.swap(expected + 1, 0, user, '');

        // Don't expect revert
        pair.swap(expected, 0, user, '');

        (reserveX, reserveY,) = pair.getReserves();
        assertEq(reserveX, reserveXOut - expected);
        assertEq(reserveY, reserveYIn + swapAmountYIn);
        vm.stopPrank();
    }

    struct ExpectedPairState {
        uint256 tokenX;
        uint256 tokenY;
        uint256 reserveXAssets;
        uint256 reserveYAssets;
    }

    function verifyPair(
        ExpectedPairState memory expected
    ) public view {
        assertEq(tokenX.balanceOf(pairAddress), expected.tokenX, 'pairAddress tokenX balance matches expected.');
        assertEq(tokenY.balanceOf(pairAddress), expected.tokenY, 'pairAddress tokenY balance matches expected.');
        (uint256 actualReserveX, uint256 actualReserveY,) = pair.getReserves();
        assertEq(actualReserveX, expected.reserveXAssets, 'pair has expected reserveX.');
        assertEq(actualReserveY, expected.reserveYAssets, 'pair has expected reserveY.');
    }

    struct ExpectedAddressState {
        address toCheck;
        uint256 balanceX;
        uint256 balanceY;
        uint256 pairDepositedX;
        uint256 pairDepositedY;
        uint256 pairLiquidity;
        uint256 pairBorrowedX;
        uint256 pairBorrowedY;
        uint256 pairBorrowedL;
    }

    function verifyAddress(
        ExpectedAddressState memory expected
    ) public view {
        assertEq(tokenX.balanceOf(expected.toCheck), expected.balanceX, 'Address has expected balance of X.');
        assertEq(tokenY.balanceOf(expected.toCheck), expected.balanceY, 'Address has expected balance of Y.');
        assertEq(
            pair.tokens(DEPOSIT_X).balanceOf(expected.toCheck),
            expected.pairDepositedX,
            'Address has expected deposit of X.'
        );
        assertEq(
            pair.tokens(DEPOSIT_Y).balanceOf(expected.toCheck),
            expected.pairDepositedY,
            'Address has expected deposit of Y.'
        );
        assertEq(
            pair.tokens(DEPOSIT_L).balanceOf(expected.toCheck),
            expected.pairLiquidity,
            'Address has expected pair liquidity.'
        );
        assertEq(
            pair.tokens(BORROW_X).balanceOf(expected.toCheck),
            expected.pairBorrowedX,
            'Address has expected borrowed X.'
        );
        assertEq(
            pair.tokens(BORROW_Y).balanceOf(expected.toCheck),
            expected.pairBorrowedY,
            'Address has expected borrowed Y.'
        );
        assertEq(
            pair.tokens(BORROW_L).balanceOf(expected.toCheck),
            expected.pairBorrowedL,
            'Address has expected borrowed L.'
        );
    }

    function runOptimisticSwapTestCase(
        address user,
        uint256 outputAmount,
        uint256 tokenXAmount,
        uint256 tokenYAmount
    ) public {
        address random = vm.addr(123_456);
        transferTokensTo(random, tokenXAmount, tokenYAmount);
        mintFor(random, tokenXAmount, tokenYAmount);

        uint256 inputAmount = computeOptimisticInputAmount(outputAmount, tokenXAmount);
        transferTokensTo(user, inputAmount, 0);

        vm.startPrank(user);
        tokenX.transfer(address(pair), inputAmount);
        // The reason we revert may change depending on the relative values of the output amount and available liquidity:
        if (outputAmount + 1 < tokenXAmount) {
            vm.expectRevert(AmmalgamPair.K.selector);
        } else {
            vm.expectRevert(AmmalgamPair.InsufficientLiquidity.selector);
        }
        pair.swap(outputAmount + 1, 0, user, '');

        // Don't expect revert
        pair.swap(outputAmount, 0, user, '');
        vm.stopPrank();
    }

    uint256 constant N = 20;

    function computeOptimisticInputAmount(
        uint256 outputAmount,
        uint256 refInputReserve
    ) private pure returns (uint256 inputAmount) {
        // The output amount is always 99.7% of the input amount, so simply reverse it out (and
        // round up if needed):
        // output = input * (1-fee)
        // output = input * (1 - n * input / R / 100)
        // 0 = -n / (R * 100) * input^2 + input - output
        // input = 1 + sqrt(1 - 4 * n * output / R / 100) / (2 * n / R / 100)
        // input = R * 50 / N * ( 1 + sqrt(1 -  N * output / (R * 25))
        // input = R / N * (50 + sqrt(2500 -  N * output * 100 / R))

        inputAmount = refInputReserve * (50 - Math.sqrt(2500 - N * outputAmount * 100 / refInputReserve)) / N;
    }

    // none interest calculation
    function activeLiquidityAssets() public view returns (uint256) {
        return pair.exposed_activeLiquidityAssets();
    }

    function computeAmountsForRepayLiquidity(
        uint256 repayLiquidity
    ) public view returns (uint256 amountX, uint256 amountY) {
        (uint112 reserveX, uint112 reserveY,) = pair.getReserves();

        uint256 _activeLiquidityAssets = activeLiquidityAssets();

        (uint112 missingXAssets, uint112 missingYAssets) = pair.exposed_missingAssets();

        return (
            DepletedAssetUtils.computeAmountsForRepayLiquidity(
                repayLiquidity, missingXAssets, missingYAssets, reserveX, reserveY, _activeLiquidityAssets
            )
        );
    }

    function moveReservesRightToYValue(
        address user,
        uint256 moveTo
    ) public returns (uint256 swapAmountXIn, uint256 swapAmountYOut) {
        // Move to the right of the buffer to set up initial test state.
        (uint256 initialMintX, uint256 initialMintY,) = pair.getReserves();
        (uint112 missingXAssets, uint112 missingYAssets) = pair.exposed_missingAssets();

        swapAmountYOut = initialMintY - moveTo;
        swapAmountXIn =
            computeExpectedSwapInAmount(swapAmountYOut, initialMintX, initialMintY, missingXAssets, missingYAssets);

        transferTokensTo(pairAddress, swapAmountXIn, 0); // skip transfer to user address and transfer again to pair.
        pair.swap(0, swapAmountYOut, user, '');
    }

    function moveReservesLeftToXValue(
        address user,
        uint256 moveTo
    ) public returns (uint256 swapAmountYIn, uint256 swapAmountXOut) {
        // Move to the left of the buffer to set up initial test state.
        (uint256 initialMintX, uint256 initialMintY,) = pair.getReserves();
        (uint112 missingXAssets, uint112 missingYAssets) = pair.exposed_missingAssets();

        swapAmountXOut = initialMintX - moveTo;
        swapAmountYIn =
            computeExpectedSwapInAmount(swapAmountXOut, initialMintY, initialMintX, missingYAssets, missingXAssets);

        transferTokensTo(pairAddress, 0, swapAmountYIn); // skip transfer to user address and transfer again to pair.
        pair.swap(swapAmountXOut, 0, user, '');
    }

    function runSwapTestCase(address user, uint256 swapAmount, uint256 tokenXAmount, uint256 tokenYAmount) public {
        transferTokensTo(user, tokenXAmount, tokenYAmount);
        mintFor(user, tokenXAmount, tokenYAmount);
        transferTokensTo(user, swapAmount, 0);
        verifySwapXToY(user, swapAmount, tokenXAmount, tokenYAmount);
    }

    function initializeLongTermBufferWithBlocks() public {
        GeometricTWAP.Observations memory obs = pair.exposed_observations();

        uint256 longTermArrayLastIndex = GeometricTWAP.LONG_TERM_ARRAY_LENGTH - 1;
        uint256 longTermBuffer = (obs.longTermIntervalConfig / DEFAULT_MID_TERM_INTERVAL) * longTermArrayLastIndex + 1;
        if (!obs.isLongTermBufferInitialized) {
            for (uint256 i = 0; i < longTermBuffer; i++) {
                mineBlock(block.number + 1, block.timestamp + DEFAULT_MID_TERM_INTERVAL);
                pair.sync();
            }
        }
    }

    function mineBlock(uint256 _block, uint256 timestamp) public {
        vm.roll(_block);
        vm.warp(timestamp);
        pair.exposed_resetTotalAssetsCached();
    }

    function swapYInXOut(address _tester, uint256 swapAmountYIn, uint256 swapAmountXOut) public {
        transferTokensTo(address(pair), 0, swapAmountYIn);
        pair.swap(swapAmountXOut, 0, _tester, '');
    }

    function swapXInYOut(address _tester, uint256 swapAmountXIn, uint256 swapAmountYOut) public {
        transferTokensTo(address(pair), swapAmountXIn, 0);
        pair.swap(0, swapAmountYOut, _tester, '');
    }

    function newBlock(
        uint256 increment
    ) public {
        mineBlock(block.number + increment, block.timestamp + (increment * DEFAULT_MID_TERM_INTERVAL));
    }

    function balanceX(
        address user
    ) public view returns (uint256) {
        return tokenX.balanceOf(user);
    }

    function balanceY(
        address user
    ) public view returns (uint256) {
        return tokenY.balanceOf(user);
    }

    function computeScalerHelper(
        uint256 tokenType
    ) public view returns (uint256) {
        return pair.totalAssets()[tokenType] * RAY / pair.tokens(tokenType).totalSupply();
    }
}
