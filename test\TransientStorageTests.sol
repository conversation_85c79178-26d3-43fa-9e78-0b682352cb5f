// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';

import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {ITokenController} from 'contracts/interfaces/tokens/ITokenController.sol';
import {
    BORROW_L,
    BORROW_X,
    BORROW_Y,
    DEPOSIT_L,
    DEPOSIT_X,
    DEPOSIT_Y
} from 'contracts/interfaces/tokens/ITokenController.sol';

import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {computeExpectedSwapInAmount} from 'test/shared/utilities.sol';

contract TransientStorageTests is Test {
    IAmmalgamPair private pair;
    address private pairAddress;

    address private random;
    address private tester;

    FactoryPairTestFixture private fixture;

    uint256 private initialMintX;
    uint256 private initialMintY;

    function setUp() public {
        tester = address(1111);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        pair = fixture.pair();
        pairAddress = address(pair);

        initialMintX = 4e18;
        initialMintY = 3e18;

        random = vm.addr(1);
        fixture.transferTokensTo(random, initialMintX, initialMintY);
        fixture.mintForAndInitializeBlocks(random, initialMintX, initialMintY) + 1000;
    }

    function testGetTransientValueReset() public {
        fixture.transferTokensTo(random, 1e18, 1e18);
        fixture.mintFor(random, 1e18, 1e18);

        assertEq(fixture.pair().exposed_getTotalAssetsCached(DEPOSIT_L), 0, 'totalAssetsCached should be 0');

        fixture.newBlock(1);
        // totalAssets are recalculated when transient storage values are 0 and duration is greater than 0
        assertEq(
            fixture.pair().exposed_activeLiquidityAssets(),
            fixture.pair().totalAssets()[DEPOSIT_L] - fixture.pair().totalAssets()[BORROW_L],
            'rawTotalAssets should match recalculated totalAssets'
        );

        fixture.pair().sync(); // sync should update the transient storage values

        assertEq(
            fixture.pair().exposed_activeLiquidityAssets(),
            fixture.pair().exposed_getTotalAssetsCached(DEPOSIT_L)
                - fixture.pair().exposed_getTotalAssetsCached(BORROW_L),
            'rawTotalAssets should match cached totalAssets'
        );
    }

    function testGetTotalAssetsXWithBoundTick() public {
        (uint112 missingXAssets, uint112 missingYAssets) = fixture.pair().exposed_missingAssets();

        uint256 swapAmountXOut = initialMintX / 2;
        uint256 swapAmountYIn =
            computeExpectedSwapInAmount(swapAmountXOut, initialMintY, initialMintX, missingYAssets, missingXAssets);

        fixture.transferTokensTo(pairAddress, 0, swapAmountYIn);
        pair.swap(swapAmountXOut, 0, random, '');

        fixture.borrowFor(random, 0, 1e18);

        assertEq(fixture.pair().exposed_getTotalAssetsCached(DEPOSIT_L), 0, 'totalAssetsCached should be 0');

        fixture.newBlock(1 days);

        uint128 totalAssetsBeforeSync = fixture.pair().totalAssets()[BORROW_Y];

        fixture.pair().sync();

        uint128 totalAssetsAfterSync = fixture.pair().totalAssets()[BORROW_Y];

        assertEq(totalAssetsBeforeSync, totalAssetsAfterSync, 'totalAssets should match');
    }

    function testGetTotalAssetsYWithBoundTick() public {
        (uint112 missingXAssets, uint112 missingYAssets) = fixture.pair().exposed_missingAssets();

        uint256 swapAmountYOut = initialMintY / 2;
        uint256 swapAmountXIn =
            computeExpectedSwapInAmount(swapAmountYOut, initialMintX, initialMintY, missingXAssets, missingYAssets);

        fixture.transferTokensTo(pairAddress, swapAmountXIn, 0);
        pair.swap(0, swapAmountYOut, random, '');

        fixture.borrowFor(random, 1e18, 0);

        fixture.newBlock(1 days);

        uint128 totalAssetsBeforeSync = fixture.pair().totalAssets()[BORROW_X];

        fixture.pair().sync();

        uint128 totalAssetsAfterSync = fixture.pair().totalAssets()[BORROW_X];

        assertEq(totalAssetsBeforeSync, totalAssetsAfterSync, 'totalAssets should match');
    }
}
