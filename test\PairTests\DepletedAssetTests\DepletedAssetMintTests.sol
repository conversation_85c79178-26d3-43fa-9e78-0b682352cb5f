// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {DepletedAssetUtils} from 'test/utils/DepletedAssetUtils.sol';

import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';

contract DepletedAssetMintTests is Test {
    IAmmalgamPair private pair;
    address private pairAddress;

    address private random;
    address private tester;

    FactoryPairTestFixture private fixture;

    uint256 private initialMintX;
    uint256 private initialMintY;
    uint256 private missingXAssets;
    uint256 private missingYAssets;
    uint256 private initialLiquidity;
    uint256 private constant BUFFER = 95;

    function setUp() public {
        tester = address(1111);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        pair = fixture.pair();
        pairAddress = address(pair);

        initialMintX = 4e18;
        initialMintY = 3e18;
        missingXAssets = 3e18;
        missingYAssets = 2e18;

        random = vm.addr(1);
        fixture.transferTokensTo(random, initialMintX, initialMintY);
        initialLiquidity = fixture.mintForAndInitializeBlocks(random, initialMintX, initialMintY) + 1000;
        fixture.borrowFor(random, missingXAssets, missingYAssets);
    }

    /**
     * @dev Mint moves unaltered invariant from (2, 6 + fee) to (3, 9 + fee) or exhausted
     * invariant from (3.1, 6 + fee) to (3.15, 9 + fee) which requires a transfer of 0.05 and 3 to mint.
     */
    function testDepletedAssetXMintPartialReplenish() public {
        // move from (4, 3) to (3.1, 6 + fee) where x is depleted
        fixture.moveReservesLeftToXValue(random, 3.1e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 mintX = 0.05e18;
        uint256 mintY = 3e18 * reserveYBefore / 6e18; // scale to account for fee.
        fixture.transferTokensTo(pairAddress, mintX, mintY);
        uint256 liquidity = pair.mint(tester);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + mintX,
                tokenY: reserveYBefore - missingYAssets + mintY,
                reserveXAssets: reserveXBefore + mintX,
                reserveYAssets: reserveYBefore + mintY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectedAddress;

        expectedAddress.toCheck = tester;
        expectedAddress.pairLiquidity = liquidity;

        fixture.verifyAddress(expectedAddress);

        assertEq(liquidity, Math.sqrt((3e18 - 2e18) * (9e18 - 6e18)), 'Mint should match change to invariant curve');
    }

    /**
     * @dev second iteration of `testDepletedAssetXMintPartialReplenish` to ensure that transferring extra x for mint results in the same liquidity.
     */
    function testDepletedAssetXMintPartialReplenishExtraX() public {
        // move from (4, 3) to (3.1, 6 + fee) where x is depleted
        fixture.moveReservesLeftToXValue(random, 3.1e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 extraX = 1e16;
        uint256 mintX = 0.05e18 + extraX;
        uint256 mintY = 3e18 * reserveYBefore / 6e18; // scale to account for fee.
        fixture.transferTokensTo(pairAddress, mintX, mintY);
        uint256 liquidity = pair.mint(tester);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + mintX,
                tokenY: reserveYBefore - missingYAssets + mintY,
                reserveXAssets: reserveXBefore + mintX,
                reserveYAssets: reserveYBefore + mintY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectedAddress;

        expectedAddress.toCheck = tester;
        expectedAddress.pairLiquidity = liquidity;

        fixture.verifyAddress(expectedAddress);

        assertEq(liquidity, Math.sqrt((3e18 - 2e18) * (9e18 - 6e18)), 'Mint should match change to invariant curve');
    }

    /**
     * @dev second iteration of `testDepletedAssetXMintPartialReplenish` to ensure that transferring extra y for mint results in the same liquidity.
     */
    function testDepletedAssetXMintPartialReplenishExtraY() public {
        // move from (4, 3) to (3.1, 6 + fee) where x is depleted
        fixture.moveReservesLeftToXValue(random, 3.1e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 extraY = 1e16;
        uint256 mintX = 0.05e18;
        uint256 mintY = 3e18 * reserveYBefore / 6e18 + extraY; // scale to account for fee.
        fixture.transferTokensTo(pairAddress, mintX, mintY);
        uint256 liquidity = pair.mint(tester);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + mintX,
                tokenY: reserveYBefore - missingYAssets + mintY,
                reserveXAssets: reserveXBefore + mintX,
                reserveYAssets: reserveYBefore + mintY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectedAddress;

        expectedAddress.toCheck = tester;
        expectedAddress.pairLiquidity = liquidity;

        fixture.verifyAddress(expectedAddress);

        assertEq(
            liquidity,
            Math.sqrt((3e18 - 2e18) * (9e18 - 6e18), Math.Rounding.Floor),
            'Mint should match change to invariant curve'
        );
    }

    /**
     * @dev Mint moves unaltered invariant from (2, 6 + fee) to (4, 12 + fee) or exhausted
     * invariant from (3.1, 6 + fee) to (4, 12 + fee) which requires a transfer of 0.9 and 6 to mint.
     */
    function testDepletedAssetXMintReplenishes() public {
        // move from (4, 3) to (3.1, 6 + fee) where x is depleted
        fixture.moveReservesLeftToXValue(random, 3.1e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 mintX = 0.9e18;
        uint256 mintY = 6e18 * reserveYBefore / 6e18; // scale to account for fee.
        uint256 expectedLiquidity = Math.sqrt((4e18 - 2e18) * (12e18 - 6e18));
        fixture.transferTokensTo(pairAddress, mintX, mintY);
        uint256 liquidity = pair.mint(tester);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + mintX,
                tokenY: reserveYBefore - missingYAssets + mintY,
                reserveXAssets: reserveXBefore + mintX,
                reserveYAssets: reserveYBefore + mintY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectedAddress;

        expectedAddress.toCheck = tester;
        expectedAddress.pairLiquidity = liquidity;

        fixture.verifyAddress(expectedAddress);

        assertEq(liquidity, expectedLiquidity, 'Mint should match change to invariant curve');
    }

    /**
     * @dev second iteration of `testDepletedAssetXMintReplenishes` to ensure that transferring extra x for mint results in the same liquidity.
     */
    function testDepletedAssetXMintReplenishesExtraX() public {
        // move from (4, 3) to (3.1, 6 + fee) where x is depleted
        fixture.moveReservesLeftToXValue(random, 3.1e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 extraX = 1e16;
        uint256 mintX = 0.9e18 + extraX;
        uint256 mintY = 6e18 * reserveYBefore / 6e18; // scale to account for fee.
        uint256 expectedLiquidity = Math.sqrt((4e18 - 2e18) * (12e18 - 6e18));
        fixture.transferTokensTo(pairAddress, mintX, mintY);
        uint256 liquidity = pair.mint(tester);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + mintX,
                tokenY: reserveYBefore - missingYAssets + mintY,
                reserveXAssets: reserveXBefore + mintX,
                reserveYAssets: reserveYBefore + mintY
            })
        );
        FactoryPairTestFixture.ExpectedAddressState memory expectedAddress;

        expectedAddress.toCheck = tester;
        expectedAddress.pairLiquidity = liquidity;
        fixture.verifyAddress(expectedAddress);

        assertEq(liquidity, expectedLiquidity, 'Mint should match change to invariant curve');
    }

    /**
     * @dev second iteration of `testDepletedAssetXMintReplenishes` to ensure that transferring extra y for mint results in the same liquidity.
     */
    function testDepletedAssetXMintReplenishesExtraY() public {
        // move from (4, 3) to (3.1, 6 + fee) where x is depleted
        fixture.moveReservesLeftToXValue(random, 3.1e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 extraY = 1e16;
        uint256 mintX = 0.9e18;
        uint256 mintY = 6e18 * reserveYBefore / 6e18 + extraY; // scale to account for fee.
        uint256 expectedLiquidity = Math.sqrt((4e18 - 2e18) * (12e18 - 6e18));
        fixture.transferTokensTo(pairAddress, mintX, mintY);
        uint256 liquidity = pair.mint(tester);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + mintX,
                tokenY: reserveYBefore - missingYAssets + mintY,
                reserveXAssets: reserveXBefore + mintX,
                reserveYAssets: reserveYBefore + mintY
            })
        );
        FactoryPairTestFixture.ExpectedAddressState memory expectedAddress;

        expectedAddress.toCheck = tester;
        expectedAddress.pairLiquidity = liquidity;
        fixture.verifyAddress(expectedAddress);

        assertEq(liquidity, expectedLiquidity, 'Mint should match change to invariant curve');
    }

    /**
     * @dev Mint moves unaltered invariant from (12, 1 + fee) to (18, 1.5 + fee) or exhausted
     * invariant from (12, 2.05) to (18, 2.075 + fee) which requires a transfer of 6 and 0.025 to mint.
     */
    function testDepletedAssetYMintPartialReplenish() public {
        // move from (4, 3) to (12 + fee, 2.05) where x is depleted
        fixture.moveReservesRightToYValue(random, 2.05e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 mintX = 6e18 * reserveXBefore / 12e18; // scale to account for fee.
        uint256 mintY = 0.025e18;
        fixture.transferTokensTo(pairAddress, mintX, mintY);
        uint256 liquidity = pair.mint(tester);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + mintX,
                tokenY: reserveYBefore - missingYAssets + mintY,
                reserveXAssets: reserveXBefore + mintX,
                reserveYAssets: reserveYBefore + mintY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectedAddress;

        expectedAddress.toCheck = tester;
        expectedAddress.pairLiquidity = liquidity;

        fixture.verifyAddress(expectedAddress);

        assertEq(liquidity, Math.sqrt((18e18 - 12e18) * (1.5e18 - 1e18)), 'Mint should match change to invariant curve');
    }

    /**
     * @dev second iteration of `testDepletedAssetYMintPartialReplenish` to ensure that transferring extra y for mint results in the same liquidity.
     */
    function testDepletedAssetYMintPartialReplenishExtraY() public {
        // move from (4, 3) to (12 + fee, 2.05) where x is depleted
        fixture.moveReservesRightToYValue(random, 2.05e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 extraY = 1e16;
        uint256 mintX = 6e18 * reserveXBefore / 12e18; // scale to account for fee.
        uint256 mintY = 0.025e18 + extraY;
        fixture.transferTokensTo(pairAddress, mintX, mintY);
        uint256 liquidity = pair.mint(tester);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + mintX,
                tokenY: reserveYBefore - missingYAssets + mintY,
                reserveXAssets: reserveXBefore + mintX,
                reserveYAssets: reserveYBefore + mintY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectedAddress;

        expectedAddress.toCheck = tester;
        expectedAddress.pairLiquidity = liquidity;

        fixture.verifyAddress(expectedAddress);

        assertEq(liquidity, Math.sqrt((18e18 - 12e18) * (1.5e18 - 1e18)), 'Mint should match change to invariant curve');
    }

    /**
     * @dev third iteration of `testDepletedAssetYMintPartialReplenish` to ensure that transferring extra x for mint results in the same liquidity.
     */
    function testDepletedAssetYMintPartialReplenishExtraX() public {
        // move from (4, 3) to (12 + fee, 2.05) where x is depleted
        fixture.moveReservesRightToYValue(random, 2.05e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 extraX = 1e16;
        uint256 mintX = 6e18 * reserveXBefore / 12e18 + extraX; // scale to account for fee.
        uint256 mintY = 0.025e18;
        fixture.transferTokensTo(pairAddress, mintX, mintY);
        uint256 liquidity = pair.mint(tester);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + mintX,
                tokenY: reserveYBefore - missingYAssets + mintY,
                reserveXAssets: reserveXBefore + mintX,
                reserveYAssets: reserveYBefore + mintY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectedAddress;

        expectedAddress.toCheck = tester;
        expectedAddress.pairLiquidity = liquidity;

        fixture.verifyAddress(expectedAddress);

        assertEq(
            liquidity,
            Math.sqrt((18e18 - 12e18) * (1.5e18 - 1e18), Math.Rounding.Floor),
            'Mint should match change to invariant curve'
        );
    }

    /**
     * @dev Mint moves unaltered invariant from (12, 1 + fee) to (30, 2.5 + fee) or exhausted
     * invariant from (12, 2.05) to (30, 2.5 + fee) which requires a transfer of 18 and 0.45 to mint.
     */
    function testDepletedAssetYMintReplenishes() public {
        // move from (4, 3) to (12 + fee, 2.05) where x is depleted
        fixture.moveReservesRightToYValue(random, 2.05e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 mintX = 18e18 * reserveXBefore / 12e18; // scale to account for fee.
        uint256 mintY = 0.45e18;
        uint256 expectedLiquidity = Math.sqrt((30e18 - 12e18) * (2.5e18 - 1e18));
        fixture.transferTokensTo(pairAddress, mintX, mintY);
        uint256 liquidity = pair.mint(tester);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + mintX,
                tokenY: reserveYBefore - missingYAssets + mintY,
                reserveXAssets: reserveXBefore + mintX,
                reserveYAssets: reserveYBefore + mintY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectedAddress;

        expectedAddress.toCheck = tester;
        expectedAddress.pairLiquidity = liquidity;

        fixture.verifyAddress(expectedAddress);

        assertEq(liquidity, expectedLiquidity, 'Mint should match change to invariant curve');
    }

    /**
     * @dev second iteration of `testDepletedAssetYMintReplenishes` to ensure that transferring extra x for mint results in the same liquidity.
     */
    function testDepletedAssetYMintReplenishesExtraX() public {
        // move from (4, 3) to (12 + fee, 2.05) where x is depleted
        fixture.moveReservesRightToYValue(random, 2.05e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 extraX = 1e16;
        uint256 mintX = 18e18 * reserveXBefore / 12e18 + extraX; // scale to account for fee.
        uint256 mintY = 0.45e18;
        uint256 expectedLiquidity = Math.sqrt((30e18 - 12e18) * (2.5e18 - 1e18), Math.Rounding.Floor);
        fixture.transferTokensTo(pairAddress, mintX, mintY);
        uint256 liquidity = pair.mint(tester);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + mintX,
                tokenY: reserveYBefore - missingYAssets + mintY,
                reserveXAssets: reserveXBefore + mintX,
                reserveYAssets: reserveYBefore + mintY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectedAddress;

        expectedAddress.toCheck = tester;
        expectedAddress.pairLiquidity = liquidity;

        fixture.verifyAddress(expectedAddress);

        assertEq(liquidity, expectedLiquidity, 'Mint should match change to invariant curve');
    }

    /**
     * @dev second iteration of `testDepletedAssetYMintReplenishes` to ensure that transferring extra y for mint results in the same liquidity.
     */
    function testDepletedAssetYMintReplenishesExtraY() public {
        // move from (4, 3) to (12 + fee, 2.05) where x is depleted
        fixture.moveReservesRightToYValue(random, 2.05e18);
        (uint256 reserveXBefore, uint256 reserveYBefore,) = pair.getReserves();

        uint256 extraY = 1e16;
        uint256 mintX = 18e18 * reserveXBefore / 12e18; // scale to account for fee.
        uint256 mintY = 0.45e18 + extraY;
        uint256 expectedLiquidity = Math.sqrt((30e18 - 12e18) * (2.5e18 - 1e18));
        fixture.transferTokensTo(pairAddress, mintX, mintY);
        uint256 liquidity = pair.mint(tester);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBefore - missingXAssets + mintX,
                tokenY: reserveYBefore - missingYAssets + mintY,
                reserveXAssets: reserveXBefore + mintX,
                reserveYAssets: reserveYBefore + mintY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectedAddress;

        expectedAddress.toCheck = tester;
        expectedAddress.pairLiquidity = liquidity;

        fixture.verifyAddress(expectedAddress);

        assertEq(liquidity, expectedLiquidity, 'Mint should match change to invariant curve');
    }
}
