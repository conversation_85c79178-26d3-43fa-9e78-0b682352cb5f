// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';

import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {Validation} from 'contracts/libraries/Validation.sol';
import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {
    IDebtDelegation, PeripheralDelegationContractExample
} from 'test/example/PeripheralDelegationContractExample.sol';
import {DelegationTestFixture} from 'test/shared/DelegationTestFixture.sol';
import {computeExpectedLiquidity, MINIMUM_LIQUIDITY} from 'test/shared/utilities.sol';
import {DEPOSIT_L, DEPOSIT_X, BOR<PERSON>W_L} from 'contracts/interfaces/tokens/ITokenController.sol';

contract BorrowLiquidityTests is Test {
    IAmmalgamFactory private factory;
    IAmmalgamPair private pair;
    address private pairAddress;

    address private tester;
    address private tester2;
    address private tester3;

    FactoryPairTestFixture private fixture;
    DelegationTestFixture private delegationFixture;

    uint256 startingTokens = 5000e18;

    function setUp() public {
        tester = vm.addr(1111);
        tester2 = vm.addr(1112);
        tester3 = vm.addr(1113);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        factory = fixture.factory();
        pair = fixture.pair();
        pairAddress = address(pair);

        fixture.transferTokensTo(tester, startingTokens, startingTokens).transferTokensTo(
            tester2, startingTokens, startingTokens
        ).transferTokensTo(tester3, startingTokens, startingTokens);

        delegationFixture = new DelegationTestFixture(pair);
    }

    function testBorrowLiquidity_byDepositX() public {
        uint256 tokenXAmount = 2e18;
        uint256 tokenYAmount = 8e18;
        uint256 testerMintLiquidity = fixture.mintForAndInitializeBlocks(tester, tokenXAmount, tokenYAmount);

        {
            uint256 initBalanceX = fixture.tokenX().balanceOf(tester2);
            uint256 initBalanceY = fixture.tokenY().balanceOf(tester2);

            uint256 depositXForBorrow = 0.4e18;
            fixture.depositFor(tester2, depositXForBorrow, 0);

            uint256 borrowAmountL = 0.2e18;
            vm.startPrank(tester2);
            (uint256 borrowAmountX, uint256 borrowAmountY) = pair.borrowLiquidity(tester2, borrowAmountL, '');

            vm.stopPrank();
            assertEq(borrowAmountX, 0.1e18);
            assertEq(borrowAmountY, 0.4e18);

            assertEq(fixture.tokenX().balanceOf(address(pair)), tokenXAmount + depositXForBorrow - borrowAmountX);
            assertEq(fixture.tokenY().balanceOf(address(pair)), tokenYAmount - borrowAmountY);

            uint256 testerLiquidity = computeExpectedLiquidity(tokenXAmount, tokenYAmount, 0, 0);
            assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), testerLiquidity);

            assertEq(pair.tokens(DEPOSIT_L).totalSupply(), testerMintLiquidity + 1000);
            assertEq(fixture.tokenX().balanceOf(tester2), initBalanceX - depositXForBorrow + borrowAmountX);
            assertEq(fixture.tokenY().balanceOf(tester2), initBalanceY + borrowAmountY);

            assertEq(pair.tokens(BORROW_L).balanceOf(tester2), borrowAmountL);
            assertEq(pair.tokens(DEPOSIT_X).totalSupply(), depositXForBorrow);
        }
    }

    function testBorrowLiquidity_andSwap() public {
        uint256 initialX = 20e18;
        uint256 initialY = 80e18;
        uint256 initialLiquidity = 40e18;
        uint256 tokenXAmount = 2e18;
        uint256 tokenYAmount = 8e18;
        uint256 totalX = initialX + tokenXAmount;
        uint256 totalY = initialY + tokenYAmount;

        initializePool(initialX, initialY);

        fixture.mintFor(tester, tokenXAmount, tokenYAmount);

        uint256 testerBalanceX = fixture.tokenX().balanceOf(tester);
        uint256 testerBalanceY = fixture.tokenY().balanceOf(tester);

        uint256 borrowAmountL = 2e18; // borrow 2 L == sqrt(2*8) / 2
        vm.prank(tester);
        (uint256 testerBorrowedLx, uint256 testerBorrowedLy) = pair.borrowLiquidity(tester, borrowAmountL, '');

        assertEq(testerBorrowedLx, 1e18);
        assertEq(testerBorrowedLy, 4e18);

        //check the tester get the borrowed amount
        assertEq(fixture.tokenX().balanceOf(tester), testerBalanceX + testerBorrowedLx);
        assertEq(fixture.tokenY().balanceOf(tester), testerBalanceY + testerBorrowedLy);

        {
            // after borrow check reserveX and reserveY
            (uint112 reserveX, uint112 reserveY,) = pair.getReserves();
            assertEq(reserveX, totalX - testerBorrowedLx);
            assertEq(reserveY, totalY - testerBorrowedLy);
        }
        //check the actual balance X and Y after borrow
        assertEq(fixture.tokenX().balanceOf(address(pair)), totalX - testerBorrowedLx);
        assertEq(fixture.tokenY().balanceOf(address(pair)), totalY - testerBorrowedLy);

        assertEq(fixture.tokenX().balanceOf(address(pair)), totalX - testerBorrowedLx);
        assertEq(fixture.tokenY().balanceOf(address(pair)), totalY - testerBorrowedLy);
        assertEq(pair.tokens(DEPOSIT_L).totalSupply(), initialLiquidity + 4e18);
        assertEq(fixture.tokenX().balanceOf(tester), testerBalanceX + testerBorrowedLx);
        assertEq(fixture.tokenY().balanceOf(tester), testerBalanceY + testerBorrowedLy);
        assertEq(pair.tokens(BORROW_L).balanceOf(tester), borrowAmountL);

        uint256 swapAmount = 1e18;

        {
            // after borrow check reserveX and reserveY
            (uint112 reserveX, uint112 reserveY,) = pair.getReserves();
            assertEq(reserveX, totalX - testerBorrowedLx);
            assertEq(reserveY, totalY - testerBorrowedLy);
        }

        fixture.verifySwapXToY(tester, swapAmount, totalX - testerBorrowedLx, totalY - testerBorrowedLy);
    }

    function testBorrowLiquidity_andMint() public {
        uint256 initialX = 20e18;
        uint256 initialY = 80e18;
        uint256 initialLiquidity = 40e18;
        uint256 tokenXAmount = 2e18;
        uint256 tokenYAmount = 8e18;
        uint256 totalX = initialX + tokenXAmount;
        uint256 totalY = initialY + tokenYAmount;

        initializePool(initialX, initialY);

        uint256 firstMintLiquidity = fixture.mintFor(tester, tokenXAmount, tokenYAmount);

        // total liquidity == 4, borrow 2
        uint256 borrowAmountL = 2e18;
        vm.prank(tester);
        (uint256 borrowAmountX, uint256 borrowAmountY) = pair.borrowLiquidity(tester, borrowAmountL, '');

        //mint borrowed x and y
        uint256 secondMintLiquidity = fixture.mintFor(tester, borrowAmountX, borrowAmountY);

        assertEq(
            pair.tokens(DEPOSIT_L).totalSupply(),
            initialLiquidity + firstMintLiquidity + secondMintLiquidity,
            'total supply should be correct'
        );
        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), firstMintLiquidity + secondMintLiquidity);

        assertEq(fixture.tokenX().balanceOf(address(pair)), totalX);
        assertEq(fixture.tokenY().balanceOf(address(pair)), totalY);

        (uint112 _reserveX, uint112 _reserveY,) = pair.getReserves();

        assertEq(_reserveX, totalX);
        assertEq(_reserveY, totalY);
    }

    function testBorrowLiquidity_andRecursiveMint() public {
        uint256 initialX = 20e18;
        uint256 initialY = 80e18;
        uint256 initialLiquidity = 40e18;
        uint256 tokenXAmount = 2e18;
        uint256 tokenYAmount = 8e18;
        uint256 totalX = initialX + tokenXAmount;
        uint256 totalY = initialY + tokenYAmount;

        initializePool(initialX, initialY);

        uint256 initBalanceX = fixture.tokenX().balanceOf(tester);
        uint256 initBalanceY = fixture.tokenY().balanceOf(tester);

        uint256 firstMintX = 2e18;
        uint256 firstMintY = 8e18;
        fixture.mintFor(tester, firstMintX, firstMintY);

        {
            // total liquidity == 4, borrow 2
            uint256 firstBorrowAmountL = 2e18;
            vm.prank(tester);
            (uint256 firstBorrowAmountX, uint256 firstBorrowAmountY) =
                pair.borrowLiquidity(tester, firstBorrowAmountL, '');

            // mint borrowed x and y
            fixture.mintFor(tester, firstBorrowAmountX, firstBorrowAmountY);

            // 6=4+2 first mint 4 + second mint 2 with borrowed Lx Ly
            assertEq(pair.tokens(DEPOSIT_L).totalSupply(), initialLiquidity + 6e18, 'liquidity is initial + 4 + 2');
            assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), 6e18, 'tester liquidity is 4 + 2');
            assertEq(pair.tokens(BORROW_L).totalSupply(), 2e18, 'testers borrowed liquidity is 2');
        }

        {
            // Reserve balance after second mint match first mint since borrowed liquidity was minted again.
            (uint112 _reserveX, uint112 _reserveY,) = pair.getReserves();
            assertEq(_reserveX, totalX, 'reserveX matches x at first mint');
            assertEq(_reserveY, totalY, 'reserveX matches y at first mint');

            // borrow 1 L = 0.5X , 2Y
            uint256 secondBorrowAmountL = 1e18;
            vm.prank(tester);
            (uint256 secondBorrowAmountX, uint256 secondBorrowAmountY) =
                pair.borrowLiquidity(tester, secondBorrowAmountL, '');
            assertEq(secondBorrowAmountX, 0.5e18, ' second borrow lx is 1/2 ');
            assertEq(secondBorrowAmountY, 2e18, 'second borrow ly is 2');

            // third mint from second borrowed x and y
            fixture.mintFor(tester, secondBorrowAmountX, secondBorrowAmountY);
        }

        assertEq(
            pair.tokens(DEPOSIT_L).totalSupply(), initialLiquidity + 7e18, 'total liquidity is initial + 4 + 2 + 1'
        );
        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), 7e18, 'tester liquidity is 4 + 2 + 1');
        assertEq(pair.tokens(BORROW_L).totalSupply(), 3e18, 'testers borrowed liquidity is 2 + 1');

        {
            // Reserve balance after third mint match first mint since borrowed liquidity was minted again.
            (uint256 _reserveX, uint256 _reserveY,) = pair.getReserves();
            assertEq(_reserveX, totalX, 'reserveX still matches x at first mint');
            assertEq(_reserveY, totalY, 'reserveY still matches y at first mint');
        }

        // after borrow - mint, borrow - mint, token balance in pool should be still the same
        assertEq(fixture.tokenX().balanceOf(address(pair)), initialX + firstMintX, 'pair x');
        assertEq(fixture.tokenY().balanceOf(address(pair)), initialY + firstMintY, 'pair y');

        assertEq(fixture.tokenX().balanceOf(tester), initBalanceX - firstMintX, 'tester x');
        assertEq(fixture.tokenY().balanceOf(tester), initBalanceY - firstMintY, 'tester y');
    }

    function testBorrowLiquidity_andMintAfterSwap() public {
        uint256 initialX = 20e18;
        uint256 initialY = 80e18;
        uint256 initialLiquidity = 40e18;
        uint256 firstMintX = 2e18;
        uint256 firstMintY = 8e18;

        initializePool(initialX, initialY);

        fixture.mintFor(tester, firstMintX, firstMintY);

        // total liquidity == 4, borrow 2
        {
            uint256 borrowAmountL = 2e18;
            vm.prank(tester);
            (uint256 borrowAmountX, uint256 borrowAmountY) = pair.borrowLiquidity(tester, borrowAmountL, '');
            assertEq(borrowAmountX, 1e18);
            assertEq(borrowAmountY, 4e18);
            uint256 swapAmount = 1e18;
            fixture.verifySwapXToY(
                tester, swapAmount, initialX + firstMintX - borrowAmountX, initialY + firstMintY - borrowAmountY
            );
        }

        //virtual balance after second mint
        (uint112 _reserveX, uint112 _reserveY,) = pair.getReserves();
        uint256 _totalSupply = pair.tokens(DEPOSIT_L).totalSupply();
        uint256 _borrowedL = pair.tokens(BORROW_L).totalSupply();

        //mint after swap, recalculate the borrowed amount based on the new ratio
        uint256 totalBorrowedLx = (_borrowedL * _reserveX) / (_totalSupply - _borrowedL);
        uint256 totalBorrowedLy = (_borrowedL * _reserveY) / (_totalSupply - _borrowedL);

        fixture.mintFor(tester, totalBorrowedLx, totalBorrowedLy);

        uint256 balanceXAfterSwap = fixture.tokenX().balanceOf(address(pair));
        uint256 balanceYAfterSwap = fixture.tokenY().balanceOf(address(pair));

        assertEq(pair.tokens(DEPOSIT_L).totalSupply(), initialLiquidity + 6e18 - 1, 'Total liquidity');
        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), 6e18 - 1, 'Tester Liquidity');
        assertEq(pair.tokens(BORROW_L).totalSupply(), 2e18);

        (_reserveX, _reserveY,) = pair.getReserves();
        assertEq(_reserveX, balanceXAfterSwap, 'check reserveX');
        assertEq(_reserveY, balanceYAfterSwap, 'check reserveY');
    }

    function testBorrowLiquidity_andBurn() public {
        uint256 tokenXAmount = 3e18;
        uint256 tokenYAmount = 3e18;
        fixture.mintForAndInitializeBlocks(tester, tokenXAmount, tokenYAmount);

        uint256 tokenXInTester2;
        uint256 tokenYInTester2;
        uint256 mintLiquidityTester2;
        {
            tokenXInTester2 = fixture.tokenX().balanceOf(tester2);
            tokenYInTester2 = fixture.tokenY().balanceOf(tester2);

            mintLiquidityTester2 = fixture.mintFor(tester2, 3e18, 3e18);

            assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester2), 3e18, 'Minted liquidity in tester2');
            assertEq(fixture.tokenX().balanceOf(tester2), tokenXInTester2 - 3e18);
            assertEq(fixture.tokenY().balanceOf(tester2), tokenYInTester2 - 3e18);
        }

        {
            fixture.depositFor(tester3, 3e18, 0); //, 'tester3 adds deposit X for borrow liquidity'
            uint256 borrowAmountL = 1e18;

            vm.startPrank(tester3);
            pair.borrowLiquidity(tester3, borrowAmountL, '');
            vm.stopPrank();
        }

        {
            vm.startPrank(tester2);
            pair.tokens(DEPOSIT_L).transfer(address(pair), mintLiquidityTester2);
            pair.burn(tester2);
            assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester2), 0);
            assertEq(fixture.tokenX().balanceOf(tester2), tokenXInTester2);
            assertEq(fixture.tokenY().balanceOf(tester2), tokenYInTester2);

            vm.stopPrank();
        }

        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), 3e18 - 1000);
    }

    function testBorrowLiquidity_andBurn_SwapBeforeBorrowL() public {
        uint256 mintX = 3e18;
        uint256 mintY = 3e18;
        uint256 mintL = 3e18;
        uint256 borrowAmountL = 0.75e18;
        fixture.mintForAndInitializeBlocks(tester, mintX, mintY);

        uint256 tokenXInTester2;
        uint256 tokenYInTester2;
        uint256 mintLiquidityTester2;
        {
            tokenXInTester2 = fixture.tokenX().balanceOf(tester2);
            tokenYInTester2 = fixture.tokenY().balanceOf(tester2);

            mintLiquidityTester2 = fixture.mintFor(tester2, mintX, mintY);

            assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester2), mintL, 'Minted liquidity in tester2');
            assertEq(fixture.tokenX().balanceOf(tester2), tokenXInTester2 - mintX);
            assertEq(fixture.tokenY().balanceOf(tester2), tokenYInTester2 - mintY);
        }

        {
            uint256 swapAmount = 1e18;
            fixture.verifySwapXToY(tester, swapAmount, mintX * 2, mintY * 2);

            uint256 depositX = 3e18;
            uint256 depositY = 0;
            fixture.depositFor(tester3, depositX, depositY);

            vm.startPrank(tester3);
            pair.borrowLiquidity(tester3, borrowAmountL, '');
            vm.stopPrank();
        }

        {
            vm.startPrank(tester2);
            pair.tokens(DEPOSIT_L).transfer(address(pair), mintLiquidityTester2);
            (uint256 burnX, uint256 burnY) = pair.burn(tester2);
            uint256 amountXAdj = burnX - mintX; // burnt more X because swap X for Y increased the X in pool
            uint256 amountYAdj = mintY - burnY; // burnt less Y because swap X for Y decreased the Y in pool
            assertEq(
                fixture.tokenX().balanceOf(tester2),
                tokenXInTester2 + amountXAdj,
                'tester2 should receive full amount minted X'
            );
            assertEq(
                fixture.tokenY().balanceOf(tester2),
                tokenYInTester2 - amountYAdj,
                'tester2 should receive full amount minted Y'
            );

            assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester2), 0);

            vm.stopPrank();
        }

        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), 3e18 - 1000);
    }

    function testBorrowLiquidity_andBurn_SwapAfterBorrowL() public {
        uint256 mintX = 3e18;
        uint256 mintY = 3e18;
        uint256 mintL = 3e18;
        fixture.mintForAndInitializeBlocks(tester, mintX, mintY);

        uint256 tokenXInTester2;
        uint256 tokenYInTester2;
        uint256 mintLiquidityTester2;
        {
            tokenXInTester2 = fixture.tokenX().balanceOf(tester2);
            tokenYInTester2 = fixture.tokenY().balanceOf(tester2);

            mintLiquidityTester2 = fixture.mintFor(tester2, mintX, mintY);

            assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester2), mintL, 'Minted liquidity in tester2');
            assertEq(fixture.tokenX().balanceOf(tester2), tokenXInTester2 - mintX);
            assertEq(fixture.tokenY().balanceOf(tester2), tokenYInTester2 - mintY);
        }

        {
            uint256 depositX = 3e18;
            uint256 depositY = 0;
            fixture.depositFor(tester3, depositX, depositY);
        }

        {
            uint256 borrowAmountL = 1e18;

            vm.startPrank(tester3);
            (uint256 borrowedX, uint256 borrowedY) = pair.borrowLiquidity(tester3, borrowAmountL, '');
            vm.stopPrank();

            uint256 swapAmount = 1e18;
            fixture.verifySwapXToY(tester3, swapAmount, mintX * 2 - borrowedX, mintY * 2 - borrowedY);
        }

        {
            vm.startPrank(tester2);
            pair.tokens(DEPOSIT_L).transfer(address(pair), mintLiquidityTester2);
            (uint256 burnX, uint256 burnY) = pair.burn(tester2);
            uint256 amountXAdj = burnX - mintX; // burnt more X because swap X for Y increased the X in pool
            uint256 amountYAdj = mintY - burnY; // burnt less Y because swap X for Y decreased the Y in pool
            assertEq(fixture.tokenX().balanceOf(tester2), tokenXInTester2 + amountXAdj);
            assertEq(fixture.tokenY().balanceOf(tester2), tokenYInTester2 - amountYAdj);

            assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester2), 0);

            vm.stopPrank();
        }

        assertEq(pair.tokens(DEPOSIT_L).balanceOf(tester), 3e18 - 1000);
    }

    function testBorrowLiquidity_andFailBurn() public {
        uint256 tokenXAmount = 3e18;
        uint256 tokenYAmount = 3e18;

        initializePool(tokenXAmount, tokenYAmount);

        fixture.mintFor(tester, tokenXAmount, tokenYAmount);

        uint256 borrowAmountL = 1e18;
        vm.startPrank(tester);
        pair.borrowLiquidity(tester, borrowAmountL, '');

        uint256 expectedLiquidity = 3e18 - MINIMUM_LIQUIDITY;
        IERC20 liquidityToken = pair.tokens(DEPOSIT_L);
        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        liquidityToken.transfer(address(pair), expectedLiquidity);
    }

    function initializePool(uint256 mintX, uint256 mintY) private {
        address random = vm.addr(2);
        fixture.transferTokensTo(random, mintX, mintY);
        fixture.mintForAndInitializeBlocks(random, mintX, mintY);
    }

    function testBorrowLiquidityApproval() public {
        uint256 initialX = 2e18;
        uint256 initialY = 8e18;

        uint256 collateralX = 0.4e18;

        uint256 borrowedL = 0.2e18; // 4 X * 50% = 4 * sqrt(8/2) /2 L * 50% = 4 L * 50% = 2 L
        uint256 borrowedLX = 0.1e18;
        uint256 borrowedLY = 0.4e18;

        fixture.mintForAndInitializeBlocks(tester3, initialX, initialY);

        fixture.depositFor(tester, collateralX, 0);

        delegationFixture.approveDebtTransferAndDelegation(tester, tester2, BORROW_L, borrowedL);
        delegationFixture.delegateBorrowLiquidity(tester, tester2, tester2, borrowedL);
        delegationFixture.verifyDelegationContractIsEmpty(BORROW_L);

        FactoryPairTestFixture.ExpectedPairState memory expectedPair = FactoryPairTestFixture.ExpectedPairState({
            tokenX: initialX + collateralX - borrowedLX,
            tokenY: initialY - borrowedLY,
            reserveXAssets: initialX - borrowedLX,
            reserveYAssets: initialY - borrowedLY
        });

        fixture.verifyPair(expectedPair);

        FactoryPairTestFixture.ExpectedAddressState memory testerState;
        testerState.toCheck = tester;
        testerState.balanceX = startingTokens - collateralX;
        testerState.balanceY = startingTokens;
        testerState.pairDepositedX = collateralX;
        testerState.pairBorrowedL = borrowedL;

        fixture.verifyAddress(testerState);

        FactoryPairTestFixture.ExpectedAddressState memory tester2State;
        tester2State.toCheck = tester2;
        tester2State.balanceX = startingTokens + borrowedLX;
        tester2State.balanceY = startingTokens + borrowedLY;

        fixture.verifyAddress(tester2State);
    }

    function testBorrowLiquidityWithoutApproval() public {
        uint256 initialX = 2e18;
        uint256 initialY = 8e18;
        fixture.mintForAndInitializeBlocks(tester3, initialX, initialY);
        uint256 collateralX = 4e18;
        uint256 borrowedL = 2e18; // 4 X * 50% = sqrt(4*16)/2 L * 50% = 4 L * 50% = 2 L

        fixture.depositFor(tester, collateralX, 0);
        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        pair.borrowLiquidity(tester, borrowedL, ''); // the caller is the address(this) wihtout approveDebt transfer from tester
    }

    function testBorrowLiquidityToAnotherAddress() public {
        uint256 firstMintX = 2e18;
        uint256 firstMintY = 8e18;
        fixture.mintForAndInitializeBlocks(tester3, firstMintX, firstMintY);
        uint256 collateralX = 0.4e18;
        uint256 borrowedL = 0.2e18; // 4 X * 50% = sqrt(4*16)/2 L * 50% = 4 L * 50% = 2 L
        uint256 borrowedLX = 0.1e18;
        uint256 borrowedLY = 0.4e18;

        // Deposit collateral
        fixture.depositFor(tester, collateralX, 0);

        // Borrow liquidity to another address
        vm.prank(tester);
        pair.borrowLiquidity(tester2, borrowedL, '');

        FactoryPairTestFixture.ExpectedPairState memory expectedPair;
        expectedPair.tokenX = firstMintX + collateralX - borrowedLX;
        expectedPair.tokenY = firstMintY - borrowedLY;
        expectedPair.reserveXAssets = firstMintX - borrowedLX;
        expectedPair.reserveYAssets = firstMintY - borrowedLY;

        fixture.verifyPair(expectedPair);

        FactoryPairTestFixture.ExpectedAddressState memory testerState;
        testerState.toCheck = tester;
        testerState.balanceX = startingTokens - collateralX;
        testerState.balanceY = startingTokens;
        testerState.pairDepositedX = collateralX;
        testerState.pairBorrowedL = borrowedL;

        fixture.verifyAddress(testerState);

        FactoryPairTestFixture.ExpectedAddressState memory tester2State;
        tester2State.toCheck = tester2;
        tester2State.balanceX = startingTokens + borrowedLX;
        tester2State.balanceY = startingTokens + borrowedLY;

        fixture.verifyAddress(tester2State);
    }

    function testDelegatedBorrowLiquidityShouldFails() public {
        uint256 firstMintX = 2e18;
        uint256 firstMintY = 8e18;
        fixture.mintForAndInitializeBlocks(tester3, firstMintX, firstMintY);
        uint256 approveAmountL = 100;
        uint256 borrowAmountL = 100;

        // no collateral borrow should fail
        delegationFixture.approveDebtTransferAndDelegation(tester, tester2, BORROW_L, approveAmountL);
        vm.expectRevert();
        delegationFixture.delegateBorrowLiquidity(tester, tester2, tester2, borrowAmountL);

        // lack of approval should fail
        // approval Amount < borrowAmount
        approveAmountL = 100;
        borrowAmountL = 200;

        // Deposit for collateral
        fixture.depositFor(tester, approveAmountL, 0);

        delegationFixture.approveDebtTransferAndDelegation(tester, tester2, BORROW_L, approveAmountL);
        vm.expectRevert();
        delegationFixture.delegateBorrowLiquidity(tester, tester2, tester2, borrowAmountL);
    }
}
