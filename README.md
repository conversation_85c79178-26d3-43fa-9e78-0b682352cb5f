# Ammalgam

#### Badges

[![Actions Status](https://github.com/Ammalgam-Protocol/core-v1/actions/workflows/ci.yml/badge.svg?branch=master)](https://github.com/Ammalgam-Protocol/core-v1/actions/workflows/ci.yml)
[![Actions Status](https://github.com/Ammalgam-Protocol/core-v1/actions/workflows/cicd.yml/badge.svg?branch=master)](https://github.com/Ammalgam-Protocol/core-v1/actions/workflows/cicd.yml)
[![codecov](https://codecov.io/github/Ammalgam-Protocol/core-v1/branch/feature/code-cov/graph/badge.svg?token=7OV7HPWL0O)](https://codecov.io/github/Ammalgam-Protocol/core-v1/tree/master)

#### Develop Branch

[![Actions Status](https://github.com/Ammalgam-Protocol/core-v1/actions/workflows/ci.yml/badge.svg?branch=develop)](https://github.com/Ammalgam-Protocol/core-v1/actions/workflows/ci.yml)

This project was forked from Uniswap V2-core

## Local Development

### Foundry Version

This project uses the nightly build of Foundry. If you encounter build issues or version conflicts, please update to the latest nightly build:

`foundryup`

### Install Dependencies

`forge install`

### Compile Contracts

`forge build --skip=Skip --sizes`

### Run Tests

`forge test`

## Running hardhat tests

`cd hardhat-tests`

### Install pnpm if not already installed

`npm install -g pnpm`

### Install dependencies

`pnpm install`

### Compile contracts

`npx hardhat compile`

### Run tests

`npx hardhat test`
