// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';

import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {IAmmalgamERC20} from 'contracts/interfaces/tokens/IAmmalgamERC20.sol';
import {IERC20DebtToken} from 'contracts/interfaces/tokens/IERC20DebtToken.sol';
import {BORROW_L, BORROW_Y} from 'contracts/interfaces/tokens/ITokenController.sol';

import {
    IDebtDelegation, PeripheralDelegationContractExample
} from 'test/example/PeripheralDelegationContractExample.sol';
import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {DelegationTestFixture} from 'test/shared/DelegationTestFixture.sol';
import {MINIMUM_LIQUIDITY} from 'test/shared/utilities.sol';

contract DebtDelegationTests is Test {
    IAmmalgamFactory private factory;
    IAmmalgamPair private pair;
    address private pairAddress;

    address private tester;
    address private tester2;

    FactoryPairTestFixture private fixture;
    DelegationTestFixture private delegationFixture;

    uint256 startingTokens = 5000e18;
    uint256 initialMintX = 100e18;
    uint256 initialMintY = 100e18;
    uint256 initialLiquidity;

    function setUp() public {
        tester = vm.addr(1111);
        tester2 = vm.addr(1112);
        address random = vm.addr(9999);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        factory = fixture.factory();
        pair = fixture.pair();
        pairAddress = address(pair);

        fixture.transferTokensTo(random, initialMintX, initialMintY);
        initialLiquidity = fixture.mintForAndInitializeBlocks(random, initialMintX, initialMintY) + 1000;

        delegationFixture = new DelegationTestFixture(pair);
    }

    function testBorrowYOnbehalfOf() public {
        uint256 depositAmountX = 100e18;
        uint256 borrowAmountY = 1e18;

        //create a deposit position for delegator to accept debt from tester2
        fixture.transferTokensTo(tester, depositAmountX, 0);
        fixture.depositFor(tester, depositAmountX, 0);

        address tester3 = vm.addr(1113);

        delegationFixture.approveDebtTransferAndDelegation(tester, tester2, BORROW_Y, borrowAmountY);
        delegationFixture.delegateBorrow(tester, tester2, tester3, 0, borrowAmountY);
        delegationFixture.verifyDelegationContractIsEmpty(BORROW_Y);

        IERC20DebtToken debtToken = delegationFixture.getDebtToken(BORROW_Y);
        assertEq(debtToken.balanceOf(tester), borrowAmountY, 'delegator should have debt');
        assertEq(debtToken.balanceOf(tester2), 0, 'delegatee should not have debt');
        assertEq(fixture.tokenY().balanceOf(tester3), borrowAmountY, 'delegatee pointed address to receive asset');
        assertEq(pair.tokens(BORROW_Y).totalSupply(), borrowAmountY, 'total supply of debt in pair');
    }

    function testBorrowLiquidityOnBehalfOf() public {
        uint256 depositAmountX = 100e18;
        uint256 depositAmountY = 100e18;
        uint256 borrowAmountL = 1e18;
        uint256 borrowedLX = 1e18;
        uint256 borrowedLY = 1e18;

        //create a deposit position for delegator to accept debt from tester2
        fixture.transferTokensTo(tester, depositAmountX, depositAmountY);
        fixture.depositFor(tester, depositAmountX, depositAmountY);

        // transfer some tokens to tester for deposit to borrow liquidity by 3rd party contract
        fixture.transferTokensTo(tester, depositAmountX, depositAmountY);

        address tester3 = vm.addr(1113);

        delegationFixture.approveDebtTransferAndDelegation(tester, tester2, BORROW_L, borrowAmountL);
        delegationFixture.delegateBorrowLiquidity(tester, tester2, tester3, borrowAmountL);
        delegationFixture.verifyDelegationContractIsEmpty(BORROW_L);

        IERC20DebtToken debtToken = delegationFixture.getDebtToken(BORROW_L);
        assertEq(debtToken.balanceOf(tester), borrowAmountL, 'delegator should have debt');
        assertEq(debtToken.balanceOf(tester2), 0, 'delegatee should not have debt');
        assertEq(fixture.tokenX().balanceOf(tester3), borrowedLX, 'borrower should have asset');
        assertEq(fixture.tokenY().balanceOf(tester3), borrowedLY, 'borrower should have asset');
        assertEq(pair.tokens(BORROW_L).totalSupply(), borrowAmountL, 'total supply of debt in pair');
    }

    function testNoApproveBorrowXYShouldFail() public {
        uint256 depositAmountX = 100e18;
        uint256 borrowAmountY = 1e18;

        //create a deposit position for delegator to accept debt from tester2
        fixture.transferTokensTo(tester, depositAmountX, 0);
        fixture.depositFor(tester, depositAmountX, 0);

        PeripheralDelegationContractExample delegationContract = new PeripheralDelegationContractExample();

        vm.prank(tester2);
        vm.expectRevert('Delegation not allowed');
        delegationContract.borrow(tester, tester2, IAmmalgamPair(pairAddress), 0, borrowAmountY);
    }

    function testNoApproveBorrowLShouldFail() public {
        uint256 depositAmountX = 100e18;
        uint256 borrowAmountL = 1e18;

        //create a deposit position for delegator to accept debt from tester2
        fixture.transferTokensTo(tester, depositAmountX, 0);
        fixture.depositFor(tester, depositAmountX, 0);

        PeripheralDelegationContractExample delegationContract = new PeripheralDelegationContractExample();

        vm.prank(tester2);
        vm.expectRevert('Delegation not allowed');
        delegationContract.borrowLiquidity(tester, tester2, IAmmalgamPair(pairAddress), borrowAmountL);
    }
}
