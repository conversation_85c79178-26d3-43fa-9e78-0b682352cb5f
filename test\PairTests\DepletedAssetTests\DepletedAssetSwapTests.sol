// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {AmmalgamPair} from 'contracts/AmmalgamPair.sol';
import {FactoryPairTestFixture, IPairHarness, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {DepletedAssetUtils} from 'test/utils/DepletedAssetUtils.sol';
import {MINIMUM_LIQUIDITY, computeExpectedSwapInAmount, computeExpectedSwapOutAmount} from 'test/shared/utilities.sol';

contract DepletedAssetSwapTests is Test {
    IPairHarness private pair;
    address private pairAddress;

    address private random;
    address private tester;

    FactoryPairTestFixture private fixture;

    uint256 private initialMintX;
    uint256 private initialMintY;
    uint256 private referenceReserveX;
    uint256 private referenceReserveY;
    uint256 private missingXAssets;
    uint256 private missingYAssets;
    uint256 private initialLiquidity;
    uint256 private constant BUFFER = 95;

    function setUp() public {
        tester = address(1111);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        pair = fixture.pair();
        pairAddress = address(pair);

        initialMintX = 4e18;
        initialMintY = 3e18;
        missingXAssets = 2e18;
        missingYAssets = 2e18;

        random = vm.addr(1);
        fixture.transferTokensTo(random, initialMintX, initialMintY);
        initialLiquidity = fixture.mintForAndInitializeBlocks(random, initialMintX, initialMintY) + 1000;
        fixture.borrowFor(random, missingXAssets, missingYAssets);
        pair.exposed_setReferenceReserves(initialMintX, initialMintY);
        (referenceReserveX, referenceReserveX) = pair.referenceReserves();
    }

    function testSwapXIn_StartInRangeAndStayInRangeToBuffer() public {
        uint256 rightBufferY = Math.ceilDiv(missingYAssets * 100, BUFFER);
        uint256 swapAmountYOut = initialMintY - rightBufferY - 1;

        uint256 swapAmountXIn =
            computeExpectedSwapInAmount(swapAmountYOut, initialMintX, referenceReserveX, initialMintY, 0, 0);

        fixture.transferTokensTo(tester, swapAmountXIn, 0);
        fixture.verifySwapXToY(tester, swapAmountXIn, initialMintX, initialMintY);
    }

    function testSwapXOut_StartInRangeAndStayInRangeToBuffer() public {
        uint256 leftBufferX = Math.ceilDiv(missingXAssets * 100, BUFFER);
        uint256 swapAmountXOut = initialMintX - leftBufferX;

        uint256 swapAmountYIn = computeExpectedSwapInAmount(swapAmountXOut, initialMintY, initialMintX);
        fixture.transferTokensTo(tester, 0, swapAmountYIn);
        fixture.verifySwapYToX(tester, swapAmountYIn, initialMintY, initialMintX);
    }

    function testSwapXIn_StartInRangeToRight() public {
        uint256 swapAmountXIn = 2e18; // right pivot is (5.7, 2.105), move to right (6, 2): 2 + 4 = 6 > 5.7
        uint256 swapAmountYOut = computeExpectedSwapOutAmount(swapAmountXIn, initialMintX, initialMintY, missingXAssets);

        fixture.transferTokensTo(tester, swapAmountXIn, 0);

        (uint256 expectedOut,,) =
            fixture.verifySwapXToY(tester, swapAmountXIn, initialMintX, initialMintY, missingYAssets);
        assertEq(expectedOut, swapAmountYOut);
    }

    function testSwapYIn_StartInRangeToLeft() public {
        uint256 swapAmountYIn = 3e18;

        fixture.transferTokensTo(tester, 0, swapAmountYIn);
        fixture.verifySwapYToX(tester, swapAmountYIn, initialMintY, initialMintX, missingXAssets);
    }

    function testSwapXOut_StartInRangeToLeft() public {
        uint256 swapAmountXOut = 1.9e18; // left pivot is (2.105, 5.7), move to left e 4 - 1.9 = 2.1 < 2.105

        uint256 swapAmountYIn = computeExpectedSwapInAmount(swapAmountXOut, initialMintY, initialMintX, missingXAssets);

        fixture.transferTokensTo(tester, 0, swapAmountYIn);

        (uint256 expectedOut,,) =
            fixture.verifySwapYToX(tester, swapAmountYIn, initialMintY, initialMintX, missingXAssets);
        assertEq(expectedOut, swapAmountXOut);
    }

    function testSwapYOut_StartInRangeToRight() public {
        uint256 swapAmountYOut = 0.9e18;

        uint256 swapAmountXIn = computeExpectedSwapOutAmount(swapAmountYOut, initialMintX, initialMintY, missingXAssets);

        fixture.transferTokensTo(tester, swapAmountXIn, 0);

        fixture.verifySwapXToY(tester, swapAmountXIn, initialMintX, initialMintY, missingYAssets);
    }

    function testSwapYOut_StartRightAndStayRight() public {
        // Move to the right of the buffer to set up initial test state.
        fixture.moveReservesRightToYValue(random, 2.1e18);

        // start (6 + firstSwapFee, 2.1) and move to (8 + firstSwapFee + secondSwapFee, 2.075)
        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        pair.exposed_setReferenceReserves(reserveX, reserveY);

        uint256 swapAmountYOut = 0.025e18;
        uint256 swapAmountXIn =
            computeExpectedSwapInAmount(swapAmountYOut, reserveX, reserveY, missingXAssets, missingYAssets);

        fixture.transferTokensTo(pairAddress, swapAmountXIn, 0); // skip transfer to user address and transfer again to pair.
        vm.expectRevert(AmmalgamPair.K.selector);
        pair.swap(0, swapAmountYOut + 10, tester, '');

        // expected out passes
        pair.swap(0, swapAmountYOut - 1, tester, '');
    }

    function testSwapXOut_StartRightAndStayRight() public {
        // Move to the right of the buffer to set up initial test state.
        fixture.moveReservesRightToYValue(random, 2.075e18);

        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        pair.exposed_setReferenceReserves(reserveX, reserveY);

        uint256 swapAmountYIn = 0.025e18;
        uint256 swapAmountXOut =
            computeExpectedSwapOutAmount(swapAmountYIn, reserveY, reserveX, missingYAssets, missingXAssets);

        fixture.transferTokensTo(pairAddress, 0, swapAmountYIn); // skip transfer to user address and transfer again to pair.
        vm.expectRevert(AmmalgamPair.K.selector);
        pair.swap(swapAmountXOut + 2, 0, tester, '');

        // expected out passes
        pair.swap(swapAmountXOut, 0, tester, '');
    }

    function testSwapXOut_StartRightAndMoveToCenter() public {
        // Move to the right of the buffer to set up initial test state.
        fixture.moveReservesRightToYValue(random, 2.1e18);

        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        pair.exposed_setReferenceReserves(reserveX, reserveY);

        uint256 swapAmountYIn = 0.9e18; // Move to (4, 3)
        uint256 swapAmountXOut =
            computeExpectedSwapOutAmount(swapAmountYIn, reserveY, reserveX, missingYAssets, missingXAssets);

        fixture.transferTokensTo(pairAddress, 0, swapAmountYIn); // skip transfer to user address and transfer again to pair.
        vm.expectRevert(AmmalgamPair.K.selector);
        pair.swap(swapAmountXOut + 1, 0, tester, '');

        // expected out passes
        pair.swap(swapAmountXOut, 0, tester, '');
    }

    function testSwapXOut_StartRightAndMoveToLeft() public {
        // Move to the right of the buffer to set up initial test state.
        fixture.moveReservesRightToYValue(random, 2.1e18);

        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        pair.exposed_setReferenceReserves(reserveX, reserveY);

        uint256 swapAmountYIn = 3.9e18; // Move to (2.1, 6)
        uint256 swapAmountXOut =
            computeExpectedSwapOutAmount(swapAmountYIn, reserveY, reserveX, missingYAssets, missingXAssets);

        fixture.transferTokensTo(pairAddress, 0, swapAmountYIn); // skip transfer to user address and transfer again to pair.
        vm.expectRevert(AmmalgamPair.K.selector);
        pair.swap(swapAmountXOut + 1, 0, tester, '');

        // expected out passes
        pair.swap(swapAmountXOut, 0, tester, '');
    }

    function testSwapYOut_StartLeftAndStayLeft() public {
        // Move to the left of the buffer to set up initial test state.// (4,3) -> (2.075, 8)
        fixture.moveReservesLeftToXValue(random, 2.075e18);

        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        pair.exposed_setReferenceReserves(reserveX, reserveY);

        // from left move right but stay in Left (2.075, 8) -> (2.1,6); start (2.075, 8 + firstSwapFee) and move to (2.1, 6 + firstSwapFee + secondSwapFee)
        uint256 swapAmountXIn = 0.025e18;
        uint256 swapAmountYOut =
            computeExpectedSwapOutAmount(swapAmountXIn, reserveX, reserveY, missingXAssets, missingYAssets);
        fixture.transferTokensTo(pairAddress, swapAmountXIn, 0); // skip transfer to user address and transfer again to pair.
        vm.expectRevert(AmmalgamPair.K.selector);
        pair.swap(0, swapAmountYOut + 2, tester, '');

        // expected out passes
        pair.swap(0, swapAmountYOut, tester, '');
    }

    function testSwapXOut_StartLeftAndStayLeft() public {
        // Move to the left of the buffer to set up initial test state.// (4,3) -> (2.1, 6)
        fixture.moveReservesLeftToXValue(random, 2.1e18);

        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        pair.exposed_setReferenceReserves(reserveX, reserveY);

        // from left move to the left again: -> (2.075, 8); start (2.1, 6 + firstSwapFee) and move to (2.075, 8 + firstSwapFee + secondSwapFee)
        uint256 swapAmountXOut = 0.025e18;
        uint256 swapAmountYIn =
            computeExpectedSwapInAmount(swapAmountXOut, reserveY, reserveX, missingYAssets, missingXAssets);

        fixture.transferTokensTo(pairAddress, 0, swapAmountYIn); // skip transfer to user address and transfer again to pair.
        vm.expectRevert(AmmalgamPair.K.selector);
        pair.swap(swapAmountXOut + 1, 0, tester, '');

        // expected out passes
        pair.swap(swapAmountXOut, 0, tester, '');
    }

    function testSwapYOut_StartLeftAndMoveToCenter() public {
        // Move to the left (4,3) -> (2.1, 6)
        fixture.moveReservesLeftToXValue(random, 2.1e18);

        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        pair.exposed_setReferenceReserves(reserveX, reserveY);

        // Move back to (4, 3)
        uint256 swapAmountXIn = 1.9e18;
        uint256 swapAmountYOut =
            computeExpectedSwapOutAmount(swapAmountXIn, reserveX, reserveY, missingXAssets, missingYAssets);

        fixture.transferTokensTo(pairAddress, swapAmountXIn, 0); // skip transfer to user address and transfer again to pair.
        vm.expectRevert(AmmalgamPair.K.selector);
        pair.swap(0, swapAmountYOut + 1, tester, '');

        // expected out passes
        pair.swap(0, swapAmountYOut, tester, '');
    }

    function testSwapXOut_StartLeftAndMoveToRight() public {
        // Move to the left (4,3) -> (2.1, 6)
        fixture.moveReservesLeftToXValue(random, 2.1e18);

        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        pair.exposed_setReferenceReserves(reserveX, reserveY);

        // from left move to the right: (2.1, 6) -> (6, 2.1); start (2.1, 6 + firstSwapFee) and move to (6, 2.1 + firstSwapFee + secondSwapFee)
        uint256 swapAmountXIn = 3.9e18;
        uint256 swapAmountYOut =
            computeExpectedSwapOutAmount(swapAmountXIn, reserveX, reserveY, missingXAssets, missingYAssets);

        fixture.transferTokensTo(pairAddress, swapAmountXIn, 0); // skip transfer to user address and transfer again to pair.
        vm.expectRevert(AmmalgamPair.K.selector);
        pair.swap(0, swapAmountYOut + 1, tester, '');

        // expected out passes
        pair.swap(0, swapAmountYOut, tester, '');
    }

    function testInsufficientOutputX() public {
        uint256 swapAmountXOut = 2e18 + 1;

        vm.expectRevert(AmmalgamPair.InsufficientLiquidity.selector);
        pair.swap(swapAmountXOut, 0, tester, '');
    }

    function testInsufficientOutputY() public {
        uint256 swapAmountYOut = 1e18 + 1;

        vm.expectRevert(AmmalgamPair.InsufficientLiquidity.selector);
        pair.swap(0, swapAmountYOut, tester, '');
    }
}
