// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {IAmmalgamFactory} from 'contracts/interfaces/factories/IAmmalgamFactory.sol';
import {Validation} from 'contracts/libraries/Validation.sol';

import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {
    adjustAmountsByTickPrice,
    adjustAmountsByTickPriceForBorrowL,
    adjustAmountsByTickPriceForBorrow,
    adjustAmountsByTickPriceForDeposit,
    computeExpectedSwapInAmountWithoutFees,
    computeExpectedSwapOutAmountWithoutFees
} from 'test/shared/utilities.sol';

contract LoanToValueIntegrationTests is Test {
    IAmmalgamFactory private factory;
    IAmmalgamPair private pair;
    address private pairAddress;

    address private random;
    address private tester = vm.addr(1112);

    uint256 private constant initialX = 8e18;
    uint256 private constant initialY = 2e18;
    uint256 private constant initialLiquidity = 4e18;
    uint256 private constant initialXPerY = initialX / initialY;
    uint8 private constant LTV = 75;

    FactoryPairTestFixture private fixture;

    function setUp() public {
        random = vm.addr(1111);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        factory = fixture.factory();
        pair = fixture.pair();
        pairAddress = address(pair);

        fixture.transferTokensTo(random, initialX, initialY);
        fixture.mintForAndInitializeBlocks(random, initialX, initialY);
    }

    function testLtvIntegrationBorrowXTwiceAgainstY() public {
        uint256 borrowX = 3e18 / 2;

        (uint256 reservesXOut, uint256 reservesYIn) = adjustAmountsByTickPrice(initialX, initialY, initialX, initialY);
        uint256 collateralSwapped = computeExpectedSwapInAmountWithoutFees(borrowX * 2, reservesYIn, reservesXOut);

        uint256 collateralY = Math.ceilDiv(100 * collateralSwapped, LTV);

        fixture.transferTokensTo(tester, 0, collateralY);
        fixture.depositFor(tester, 0, collateralY);

        fixture.borrowFor(tester, borrowX, 0);
        verifyMaxBorrow(tester, borrowX, 0);
    }

    function testLtvIntegrationBorrowYTwiceAgainstX() public {
        uint256 borrowY = 0.75e18 / 2;

        (uint256 reservesXIn, uint256 reservesYOut) =
            adjustAmountsByTickPriceForBorrowL(initialX, initialY, initialX, initialY);
        uint256 collateralSwapped = computeExpectedSwapInAmountWithoutFees(borrowY * 2, reservesXIn, reservesYOut);

        uint256 collateralX = Math.ceilDiv(100 * collateralSwapped, LTV);
        fixture.transferTokensTo(tester, collateralX, 0);
        fixture.depositFor(tester, collateralX, 0);

        fixture.borrowFor(tester, 0, borrowY);
        verifyMaxBorrow(tester, 0, borrowY);
    }

    function testLtvIntegrationBorrowLTwiceAgainstX() public {
        uint256 borrowLY = 0.5e18;
        uint256 borrowLX = 2e18;

        (uint256 borrowLXAdj, uint256 borrowLYAdj) =
            adjustAmountsByTickPriceForBorrowL(borrowLX, borrowLY, initialX, initialY);

        (uint256 reservesXIn, uint256 reservesYOut) =
            adjustAmountsByTickPriceForBorrowL(initialX - borrowLX, initialY - borrowLY, initialX, initialY);
        uint256 swapInX = computeExpectedSwapInAmountWithoutFees(borrowLYAdj, reservesXIn, reservesYOut);

        uint256 collateralNeededX = Math.ceilDiv(swapInX * 100, LTV);
        uint256 collateralX = collateralNeededX + borrowLXAdj;

        uint256 borrowL = Math.sqrt(borrowLX * borrowLY) / 2;

        fixture.transferTokensTo(tester, collateralX, 0);
        fixture.depositFor(tester, collateralX, 0);

        fixture.borrowLiquidityFor(tester, borrowL);
        verifyMaxBorrowLiquidity(tester, borrowL - 1);
    }

    function testLtvIntegrationProvideXCollateralAfterBorrowingXFails() public {
        uint256 collateralY = 1.7e18;

        (uint256 reservesXOut, uint256 reservesYIn) = adjustAmountsByTickPrice(initialX, initialY, initialX, initialY);
        uint256 swapOutX = computeExpectedSwapOutAmountWithoutFees(collateralY * LTV / 100, reservesYIn, reservesXOut);

        uint256 borrowX = swapOutX / 2; // max borrow is not being tested here

        fixture.transferTokensTo(tester, 0, collateralY);
        fixture.depositFor(tester, 0, collateralY);

        fixture.borrowFor(tester, borrowX, 0);

        uint256 collateralX = 1.0e18;
        fixture.transferTokensTo(address(pair), collateralX, 0);
        vm.expectRevert(Validation.AmmalgamCannotBorrowAgainstSameCollateral.selector);
        pair.deposit(tester);
    }

    function testLtvIntegrationProvideYCollateralAfterBorrowingYFails() public {
        uint256 collateralX = 10e18;

        (uint256 reservesXIn, uint256 reservesYOut) = adjustAmountsByTickPrice(initialX, initialY, initialX, initialY);
        uint256 swapOutY = computeExpectedSwapOutAmountWithoutFees(collateralX * LTV / 100, reservesXIn, reservesYOut);

        uint256 borrowY = swapOutY / 2;

        fixture.transferTokensTo(tester, collateralX, 0);
        fixture.depositFor(tester, collateralX, 0);

        fixture.borrowFor(tester, 0, borrowY);

        uint256 collateralY = 17e18;
        fixture.transferTokensTo(address(pair), 0, collateralY);
        vm.expectRevert(Validation.AmmalgamCannotBorrowAgainstSameCollateral.selector);
        pair.deposit(tester);
    }

    function testLtvIntegrationWithdrawTwiceYWithXAgainstY() public {
        uint256 collateralY = 3e18;
        uint256 withdrawY = collateralY / 3;

        // do not need to adjust max tick plus one here because both borrow and deposit are using minTick for borrow X and deposit Y
        (uint256 reservesXOut, uint256 reservesYIn) = adjustAmountsByTickPrice(initialX, initialY, initialX, initialY);
        uint256 borrowX = computeExpectedSwapOutAmountWithoutFees(
            (collateralY - 2 * withdrawY) * LTV / 100, reservesYIn, reservesXOut
        );

        fixture.transferTokensTo(tester, 0, collateralY);
        fixture.depositFor(tester, 0, collateralY);
        fixture.borrowFor(tester, borrowX, 0);
        fixture.withdrawFor(tester, 0, withdrawY);

        verifyMaxWithdraw(tester, 0, withdrawY - 1);
    }

    function testLtvIntegrationWithdrawTwiceXWithYAgainstX() public {
        uint256 collateralX = 3e18;
        uint256 withdrawX = collateralX / 3;

        (uint256 reservesXIn, uint256 reservesYOut) =
            adjustAmountsByTickPriceForBorrowL(initialX, initialY, initialX, initialY);
        uint256 borrowY = computeExpectedSwapOutAmountWithoutFees(
            (collateralX - 2 * withdrawX) * LTV / 100, reservesXIn, reservesYOut
        );

        fixture.transferTokensTo(tester, collateralX, 0);
        fixture.depositFor(tester, collateralX, 0);
        fixture.borrowFor(tester, 0, borrowY);
        fixture.withdrawFor(tester, withdrawX, 0);

        verifyMaxWithdraw(tester, withdrawX - 2, 0);
    }

    function testLtvIntegrationBurnTwiceWithXAgainstL() public {
        uint256 mintX = 3e18;
        uint256 mintY = 0.75e18;
        uint256 withdrawL = Math.sqrt(mintX * mintY) / 3;

        // withdrawing 1/3 twice leaves 1/3 after.
        uint256 mintXAfterWithdraw = mintX / 3;
        uint256 mintYAfterWithdraw = mintY / 3;
        uint256 reservesXAfterWithdraw = mintXAfterWithdraw + initialX;
        uint256 reservesYAfterWithdraw = mintYAfterWithdraw + initialY;

        (uint256 mintXAdj, uint256 mintYAdj) =
            adjustAmountsByTickPrice(mintXAfterWithdraw, mintYAfterWithdraw, initialX, initialY);

        // Can borrow up their current liquidity in x plus 75% of the liquidity in y.
        (uint256 reservesXOut, uint256 reservesYIn) =
            adjustAmountsByTickPrice(reservesXAfterWithdraw, reservesYAfterWithdraw, initialX, initialY);
        uint256 borrowX =
            mintXAdj + computeExpectedSwapOutAmountWithoutFees(mintYAdj * LTV / 100, reservesYIn, reservesXOut);

        fixture.transferTokensTo(tester, mintX, mintY);
        fixture.mintFor(tester, mintX, mintY);
        fixture.borrowFor(tester, borrowX, 0);
        fixture.burnFor(tester, withdrawL);

        verifyMaxBurn(tester, withdrawL);
    }

    function testLtvIntegrationBurnTwiceWithYAgainstL() public {
        uint256 mintX = 3e18;
        uint256 mintY = 0.75e18;
        uint256 withdrawL = Math.sqrt(mintX * mintY) / 3;

        // withdrawing 1/3 twice leaves 1/3 after.
        uint256 mintXAfterWithdraw = mintX / 3;
        uint256 mintYAfterWithdraw = mintY / 3;
        uint256 reservesXAfterWithdraw = mintXAfterWithdraw + initialX;
        uint256 reservesYAfterWithdraw = mintYAfterWithdraw + initialY;

        (uint256 mintXAdj, uint256 mintYAdj) =
            adjustAmountsByTickPriceForBorrowL(mintXAfterWithdraw, mintYAfterWithdraw, initialX, initialY);

        // Can borrow up their current liquidity in y plus 75% of the liquidity in x.
        (uint256 reservesXIn, uint256 reservesYOut) =
            adjustAmountsByTickPriceForBorrowL(reservesXAfterWithdraw, reservesYAfterWithdraw, initialX, initialY);
        uint256 borrowY =
            mintYAdj + computeExpectedSwapOutAmountWithoutFees(mintXAdj * LTV / 100, reservesXIn, reservesYOut);

        fixture.transferTokensTo(tester, mintX, mintY);
        fixture.mintFor(tester, mintX, mintY);
        fixture.borrowFor(tester, 0, borrowY);
        fixture.burnFor(tester, withdrawL);

        verifyMaxBurn(tester, withdrawL);
    }

    function testLtvIntegrationBorrowXAgainstYWithExternalLiquidity() public {
        vm.startPrank(address(fixture));

        pair.updateExternalLiquidity(uint112(2 * initialLiquidity));
        vm.stopPrank();

        uint256 borrowX = 3e18;

        (uint256 reservesXOut, uint256 reservesYIn) =
            adjustAmountsByTickPrice(3 * initialX, 3 * initialY, initialX, initialY);
        uint256 collateralSwapped = computeExpectedSwapInAmountWithoutFees(borrowX, reservesYIn, reservesXOut);
        uint256 collateralY = Math.ceilDiv(100 * collateralSwapped, LTV);

        vm.stopPrank();

        fixture.transferTokensTo(tester, 0, collateralY);
        fixture.depositFor(tester, 0, collateralY);

        verifyMaxBorrow(tester, borrowX - 2, 0);
    }

    /*
     *  TEST HELPER METHODS
     */

    /**
     * @dev Since values are divided, truncated, than multiplied again, we need to find the largest multiple of the
     *   factor less than or equal to the value.
     * @param value The value to find the largest multiple of the factor less than or equal to.
     * @param factor The factor to find the largest multiple of.
     */
    function calculateLargestMultipleOfFactorLessOrEqualToValue(
        uint256 value,
        uint256 factor
    ) private pure returns (uint256) {
        return value / factor * factor;
    }

    function verifyMaxBorrow(address borrower, uint256 borrowX, uint256 borrowY) private {
        verifyMaxBorrow(borrower, borrowX, borrowY, 1);
    }

    function verifyMaxBorrow(address borrower, uint256 borrowX, uint256 borrowY, uint256 increment) private {
        if (borrowY == 0) {
            vm.expectRevert(Validation.AmmalgamLTV.selector);
            fixture.borrowFor(borrower, borrowX + increment, borrowY);
        } else if (borrowX == 0) {
            vm.expectRevert(Validation.AmmalgamLTV.selector);
            fixture.borrowFor(borrower, borrowX, borrowY + increment);
        } else {
            vm.expectRevert(Validation.AmmalgamLTV.selector);
            fixture.borrowFor(borrower, borrowX + increment, borrowY);
            vm.expectRevert(Validation.AmmalgamLTV.selector);
            fixture.borrowFor(borrower, borrowX, borrowY + increment);
        }

        // max borrow approved
        fixture.borrowFor(borrower, borrowX, borrowY);
    }

    function verifyMaxBorrowLiquidity(address borrower, uint256 borrowL) private {
        vm.expectRevert(Validation.AmmalgamLTV.selector);
        fixture.borrowLiquidityFor(borrower, borrowL + 1);

        // max borrow approved
        fixture.borrowLiquidityFor(borrower, borrowL);
    }

    function verifyMaxWithdraw(address owner, uint256 amountX, uint256 amountY) private {
        if (amountY == 0) {
            vm.expectRevert(Validation.AmmalgamLTV.selector);
            fixture.withdrawFor(owner, amountX + 1, amountY);
        } else if (amountX == 0) {
            vm.expectRevert(Validation.AmmalgamLTV.selector);
            fixture.withdrawFor(owner, amountX, amountY + 1);
        } else {
            vm.expectRevert(Validation.AmmalgamLTV.selector);
            fixture.withdrawFor(owner, amountX + 1, amountY);
            vm.expectRevert(Validation.AmmalgamLTV.selector);
            fixture.withdrawFor(owner, amountX, amountY + 1);
        }

        // max withdraw approved
        fixture.withdrawFor(owner, amountX, amountY);
    }

    function verifyMaxBurn(address owner, uint256 amountL) private {
        vm.expectRevert(Validation.AmmalgamLTV.selector);
        fixture.burnFor(owner, amountL + 1);

        // max borrow approved
        fixture.burnFor(owner, amountL);
    }
}
