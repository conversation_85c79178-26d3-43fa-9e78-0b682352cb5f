// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';

import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';
import {IERC4626} from '@openzeppelin/contracts/interfaces/IERC4626.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {MathLib, WAD} from '@morpho-org/morpho-blue/src/libraries/MathLib.sol';

import {DEPOSIT_L, DEPOSIT_X, BORROW_L, BORROW_X} from 'contracts/interfaces/tokens/ITokenController.sol';
import {Interest} from 'contracts/libraries/Interest.sol';

import {TickMath} from 'contracts/libraries/TickMath.sol';
import {Validation} from 'contracts/libraries/Validation.sol';

import {FactoryPairTestFixture, MAX_TOKEN, IPairHarness} from 'test/shared/FactoryPairTestFixture.sol';
import {
    InterestFixture,
    MintAndBurnInputParams,
    BorrowAndRepayXYInputParams,
    DepositAndWithdrawInputParams,
    RAY
} from 'test/InterestTests/InterestFixture.sol';

import {getExpectedInterest} from 'test/shared/utilities.sol';
import {TOLERANCE_1} from 'test/utils/constants.sol';

contract InterestXIntegrationTests is Test {
    using MathLib for uint256;

    uint112 constant ZERO_DEPOSITED_X_ASSETS = 0;
    uint112 constant ZERO_BORROWED_L_ASSETS = 0;

    FactoryPairTestFixture public fixture;
    InterestFixture public interestFixture;

    address random = address(0xa0);
    address borrower1Addr = address(0xb1);
    address borrower2Addr = address(0xb2);
    address minter1Addr = address(0xd1);
    address minter2Addr = address(0xd2);
    address lender1Addr = address(0xc1);
    address lender2Addr = address(0xc2);

    uint256 totalBShares;
    uint256 totalDShares;

    uint256 initialLX;
    uint256 initialLY;
    uint256 initialL;

    MintAndBurnInputParams user1Params;
    MintAndBurnInputParams user2Params;
    DepositAndWithdrawInputParams lender1Params;
    DepositAndWithdrawInputParams lender2Params;
    BorrowAndRepayXYInputParams borrower1Params;

    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        interestFixture = new InterestFixture(fixture);

        initPool(100e18, 50e18);

        setUpUsers();
    }

    function initPool(uint256 _initialLX, uint256 _initialLY) private {
        fixture.transferTokensTo(random, _initialLX, _initialLY);
        initialL = fixture.mintForAndInitializeBlocks(random, _initialLX, _initialLY);

        initialLX = _initialLX;
        initialLY = _initialLY;
    }

    function setUpUsers() private {
        user1Params.userAddress = minter1Addr;
        user1Params.mintedLX = 200e18;
        user1Params.mintedLY = 100e18;

        user2Params.userAddress = minter2Addr;
        user2Params.mintedLX = 200e18;
        user2Params.mintedLY = 100e18;

        lender1Params.userAddress = lender1Addr;
        lender1Params.depositedXAssets = 150e18;

        lender2Params.userAddress = lender2Addr;
        lender2Params.depositedXAssets = 150e18; // will be recalculated after deposit

        borrower1Params.userAddress = borrower1Addr;
        borrower1Params.borrowedXAssets = 13e18; // picked number to be easier diff
        borrower1Params.collateralY = type(uint112).max;
    }

    function testCanSkimStealInterestForX() public {
        user1Params = interestFixture.mintHelper(user1Params);
        interestFixture.borrowHelper(borrower1Params);
        interestFixture.warpForwardBy(1 days);

        IERC20 tokenX = fixture.tokenX();
        IERC20 tokenY = fixture.tokenY();

        uint256 tokenXBalance = tokenX.balanceOf(address(fixture.pair()));
        uint256 tokenYBalance = tokenY.balanceOf(address(fixture.pair()));

        address addressThis = address(this);

        assertEq(tokenX.balanceOf(addressThis), 0, 'tokenX balance should be 0');
        assertEq(tokenY.balanceOf(addressThis), 0, 'tokenY balance should be 0');

        fixture.pair().skim(addressThis);

        assertEq(tokenX.balanceOf(address(fixture.pair())), tokenXBalance, 'tokenX balance should not change');
        assertEq(tokenY.balanceOf(address(fixture.pair())), tokenYBalance, 'tokenY balance should not change');

        assertEq(tokenX.balanceOf(addressThis), 0, 'tokenX balance should be 0');
        assertEq(tokenY.balanceOf(addressThis), 0, 'tokenY balance should be 0');
    }

    function testInterest_BorrowXAgainstL_ReserveIncreased() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp and sync
        uint256 duration = 365 days;
        interestFixture.warpAndSkim(duration);

        // compute interest
        uint256 totalBorrowedXAssets = borrower1Params.borrowedXAssets;
        uint256 totalDepositedXAssets = getTotalDepositedXAssets(
            ZERO_DEPOSITED_X_ASSETS,
            uint112(initialL + user1Params.mintedL),
            ZERO_BORROWED_L_ASSETS,
            lastLendingCumulativeSum,
            duration
        );

        uint256 totalBorrowXInterest = getExpectedInterest(duration, totalBorrowedXAssets, totalDepositedXAssets);
        uint256 protocolFeeX = totalBorrowXInterest * Interest.LENDING_FEE_RATE / 100;
        uint256 netBorrowXInterest = totalBorrowXInterest - protocolFeeX;

        // verify reserveX and reserveY
        (uint112 reserveX, uint112 reserveY,) = fixture.pair().getReserves();

        assertEq(
            reserveX,
            uint112(initialLX + user1Params.mintedLX + netBorrowXInterest),
            'reserveX should match after interest accrual 1'
        );

        assertEq(reserveY, uint112(initialLY + user1Params.mintedLY), 'reserveY should match after interest accrual 1');
    }

    function testInterest_BorrowXAgainstL_OneBorrower_OneMintAndBurnAndCollectInterest() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp and sync
        uint256 duration = 365 days;
        interestFixture.warpAndSkim(duration);

        // compute interest
        uint256 totalBorrowedXAssets = borrower1Params.borrowedXAssets;

        uint112 depositedLAssets = uint112(initialL + user1Params.mintedL);
        uint256 totalDepositedXAssets = getTotalDepositedXAssets(
            ZERO_DEPOSITED_X_ASSETS, depositedLAssets, ZERO_BORROWED_L_ASSETS, lastLendingCumulativeSum, duration
        );

        uint256 totalBorrowXInterest = getExpectedInterest(duration, totalBorrowedXAssets, totalDepositedXAssets);
        uint256 protocolFeeX = totalBorrowXInterest * Interest.LENDING_FEE_RATE / 100;
        uint256 netBorrowXInterest = totalBorrowXInterest - protocolFeeX;

        uint112 reserveXWithInterest = uint112(initialLX + user1Params.mintedLX) + uint112(netBorrowXInterest);

        // Note: get activeLiquidityAssets before burns to be used computing expected burn amount.
        (,, uint256 activeLiquidityAssetsBeforeBurns) =
            fixture.pair().exposed_getDepositAndBorrowAndActiveLiquidityAssets();

        // minter1 burn
        user1Params.burnRate = 100;
        user1Params = interestFixture.burnHelper(user1Params);

        // verify burn results
        assertEq(
            fixture.pair().tokens(DEPOSIT_L).balanceOf(user1Params.userAddress),
            0,
            'Liquidity balance should be 0 after user1 burnX'
        );

        uint256 user1ActualBurnedX = fixture.tokenX().balanceOf(user1Params.userAddress);
        uint256 user1ExpectedBurnedX = user1Params.burnedL * reserveXWithInterest / activeLiquidityAssetsBeforeBurns;

        assertEq(user1ActualBurnedX, user1ExpectedBurnedX, 'user1Params burnX amount should match');
    }

    function testInterest_BorrowXAgainstL_TriggeredByMint() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp without sync
        uint256 duration = 365 days;
        interestFixture.warpForwardBy(duration);

        // Interest accrual is triggered by User2's minting action instead of sync
        user2Params = interestFixture.mintHelper(user2Params);

        // compute interest
        uint256 totalBorrowedXAssets = borrower1Params.borrowedXAssets;

        uint112 depositedLAssets = uint112(initialL + user1Params.mintedL);
        uint256 totalDepositedXAssets = getTotalDepositedXAssets(
            ZERO_DEPOSITED_X_ASSETS, depositedLAssets, ZERO_BORROWED_L_ASSETS, lastLendingCumulativeSum, duration
        );

        uint256 totalBorrowXInterest = getExpectedInterest(duration, totalBorrowedXAssets, totalDepositedXAssets);
        uint256 protocolFeeX = totalBorrowXInterest * Interest.LENDING_FEE_RATE / 100;
        uint256 netBorrowXInterest = totalBorrowXInterest - protocolFeeX;
        // verify reserveX
        (uint112 reserveX2,,) = fixture.pair().getReserves();

        assertEq(
            initialLX + user1Params.mintedLX + user2Params.mintedLX + netBorrowXInterest,
            reserveX2,
            'reserveX should match after user2 mint'
        );
    }

    function testInterest_BorrowXAgainstL_TriggeredByMinter2AndCollectingInterest() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp without sync
        uint256[2] memory duration = [uint256(365 days), uint256(24 hours)];
        interestFixture.warpForwardBy(duration[0]);

        // Interest accrual from period1 is triggered by User2's minting action instead of sync
        user2Params = interestFixture.mintHelper(user2Params);

        // compute interest from period1
        uint256 totalBorrowedXAssets = borrower1Params.borrowedXAssets;

        uint112 depositedLAssets = uint112(initialL + user1Params.mintedL);
        uint256 totalDepositedXAssets = getTotalDepositedXAssets(
            ZERO_DEPOSITED_X_ASSETS, depositedLAssets, ZERO_BORROWED_L_ASSETS, lastLendingCumulativeSum, duration[0]
        );

        uint256 borrowInterestInFirstPeriod =
            getExpectedInterest(duration[0], totalBorrowedXAssets, totalDepositedXAssets);

        uint256 protocolFeeX1 = borrowInterestInFirstPeriod * Interest.LENDING_FEE_RATE / 100;

        // warp and sync to accrue interest from period2
        interestFixture.warpAndSkim(duration[1]);

        // compute interest from period2
        totalBorrowedXAssets = borrower1Params.borrowedXAssets + borrowInterestInFirstPeriod;

        depositedLAssets = uint112(initialL + user1Params.mintedL + user2Params.mintedL);
        totalDepositedXAssets = getTotalDepositedXAssets(
            protocolFeeX1, depositedLAssets, ZERO_BORROWED_L_ASSETS, lastLendingCumulativeSum, duration[1]
        );

        // verify reserveX
        (uint112 reserveXSecondPeriod,,) = fixture.pair().getReserves();

        // Note: get activeLiquidityAssets before burns to be used computing expected burn amount.
        (,, uint256 activeLiquidityAssetsBeforeBurns) =
            fixture.pair().exposed_getDepositAndBorrowAndActiveLiquidityAssets();

        // minter1 burn
        user1Params.burnRate = 100;
        user1Params = interestFixture.burnHelper(user1Params);

        // verify burn results
        assertEq(
            fixture.pair().tokens(DEPOSIT_L).balanceOf(user1Params.userAddress),
            0,
            'Liquidity balance should be 0 after user1 burnX'
        );

        uint256 user1ActualBurnedX = fixture.tokenX().balanceOf(user1Params.userAddress);
        uint256 user1ExpectedBurnedX = user1Params.burnedL * reserveXSecondPeriod / activeLiquidityAssetsBeforeBurns;

        assertApproxEqRel(
            user1ActualBurnedX, user1ExpectedBurnedX, TOLERANCE_1, 'user1Params burnX amount should match'
        );

        (uint112 reserveXAfterFirstUserBurn,,) = fixture.pair().getReserves();
        // Note: get activeLiquidityAssets before burns to be used computing expected burn amount.
        (,, activeLiquidityAssetsBeforeBurns) = fixture.pair().exposed_getDepositAndBorrowAndActiveLiquidityAssets();

        // minter2 burn
        user2Params.burnRate = 100;
        user2Params = interestFixture.burnHelper(user2Params);

        // verify burn results
        assertEq(
            fixture.pair().tokens(DEPOSIT_L).balanceOf(user2Params.userAddress),
            0,
            'Liquidity balance should be 0 after user2 burnX'
        );

        uint256 user2ActualBurnedX = fixture.tokenX().balanceOf(user2Params.userAddress);
        uint256 user2ExpectedBurnedX =
            user2Params.burnedL * reserveXAfterFirstUserBurn / activeLiquidityAssetsBeforeBurns;

        assertApproxEqRel(
            user2ActualBurnedX, user2ExpectedBurnedX, TOLERANCE_1, 'user2Params burnX amount should match'
        );
    }

    function testInterest_BorrowXAgainstL_User2MintTriggerInterestButAlsoCollectingInterest() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        uint256[2] memory duration = [uint256(365 days), uint256(24 hours)];

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;
        // warp without accrue interest
        interestFixture.warpForwardBy(duration[0]);
        (uint112 reserveX0, uint112 reserveY0,) = fixture.pair().getReserves();

        // Note: user2 mintedLY is calculated by user2's mintedLX due to interest has been added up on reserveX
        user2Params.mintedLY = user2Params.mintedLX * reserveY0 / reserveX0;
        user2Params = interestFixture.mintHelper(user2Params); // mint to trigger interest accrual for period 1

        // compute interest from period1 triggered by mint
        uint256 totalBorrowedXAssets = borrower1Params.borrowedXAssets;

        uint112 depositedLAssets = uint112(initialL + user1Params.mintedL);
        uint256 totalDepositedXAssets = getTotalDepositedXAssets(
            ZERO_DEPOSITED_X_ASSETS, depositedLAssets, ZERO_BORROWED_L_ASSETS, lastLendingCumulativeSum, duration[0]
        );

        uint256 borrowInterestInFirstPeriod =
            getExpectedInterest(duration[0], totalBorrowedXAssets, totalDepositedXAssets);

        lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;
        // warp and sync to accrue interest
        interestFixture.warpAndSkim(duration[1]);

        totalBorrowedXAssets = borrower1Params.borrowedXAssets + borrowInterestInFirstPeriod;

        depositedLAssets = uint112(initialL + user1Params.mintedL + user2Params.mintedL);
        totalDepositedXAssets = getTotalDepositedXAssets(
            ZERO_DEPOSITED_X_ASSETS, depositedLAssets, ZERO_BORROWED_L_ASSETS, lastLendingCumulativeSum, duration[1]
        );

        // verify reserveX
        (uint112 reserveX2,,) = fixture.pair().getReserves();

        // Note: get activeLiquidityAssets before burns to be used computing expected burn amount.
        (,, uint256 activeLiquidityAssetsBeforeBurns) =
            fixture.pair().exposed_getDepositAndBorrowAndActiveLiquidityAssets();

        // minter1 burn
        user1Params.burnRate = 100;
        user1Params = interestFixture.burnHelper(user1Params);

        (uint112 reserveXAfterUser1Burn,,) = fixture.pair().getReserves();

        // verify burn results
        uint256 user1ActualBurnedX = fixture.tokenX().balanceOf(user1Params.userAddress);
        uint256 user1ExpectedBurnedX = user1Params.burnedL * reserveX2 / activeLiquidityAssetsBeforeBurns;
        assertEq(
            fixture.pair().tokens(DEPOSIT_L).balanceOf(user1Params.userAddress),
            0,
            'Liquidity balance should be 0 after user1 burnX'
        );
        assertApproxEqRel(
            user1ActualBurnedX, user1ExpectedBurnedX, TOLERANCE_1, 'user1Params burnX amount should match'
        );

        // Note: get activeLiquidityAssets before burns to be used computing expected burn amount.
        (,, activeLiquidityAssetsBeforeBurns) = fixture.pair().exposed_getDepositAndBorrowAndActiveLiquidityAssets();

        // minter2 burn
        user2Params.burnRate = 100;
        user2Params = interestFixture.burnHelper(user2Params);

        // verify burn results
        uint256 user2ActualBurnedX = fixture.tokenX().balanceOf(user2Params.userAddress);
        uint256 user2ExpectedBurnedX = user2Params.burnedL * reserveXAfterUser1Burn / activeLiquidityAssetsBeforeBurns;

        assertEq(
            fixture.pair().tokens(DEPOSIT_L).balanceOf(user2Params.userAddress),
            0,
            'Liquidity balance should be 0 after user2 burnX'
        );

        assertApproxEqRel(
            user2ActualBurnedX, user2ExpectedBurnedX, TOLERANCE_1, 'user2Params burnX amount should match'
        );
    }

    function getTotalDepositedAssets(
        uint256 depositedXOrYAssets,
        int56 lastLendingCumulativeSum,
        uint256 duration
    ) private view returns (uint256) {
        int16 lendingStateTick = fixture.pair().exposed_getLendingStateTick(lastLendingCumulativeSum, duration); //6931;

        (uint256 reserveXOrYAtLendingTick,) = Interest.getReservesAtTick(
            fixture.pair().totalAssets()[DEPOSIT_L] - fixture.pair().totalAssets()[BORROW_L], lendingStateTick
        );

        return reserveXOrYAtLendingTick + depositedXOrYAssets;
    }

    function testInterest_BorrowXAgainstXL_SingleDepositAndBorrow() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // deposit1
        lender1Params = interestFixture.depositHelper(lender1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp without sync
        uint256 duration = uint256(365 days);
        interestFixture.warpAndSkim(duration);

        // compute expected interest for borrowX
        uint256 totalBorrowedXAssets = borrower1Params.borrowedXAssets;
        uint256 depositedXAssets = lender1Params.depositedXShares; // initially the shares is assets

        uint256 totalDepositedXAssets = getTotalDepositedXAssets(
            depositedXAssets,
            uint112(initialL + user1Params.mintedL),
            ZERO_BORROWED_L_ASSETS,
            lastLendingCumulativeSum,
            duration
        );

        uint256 totalBorrowXInterest = getExpectedInterest(duration, totalBorrowedXAssets, totalDepositedXAssets);
        uint256 protocolFeeX = totalBorrowXInterest * Interest.LENDING_FEE_RATE / 100;
        uint256 netBorrowXInterest = totalBorrowXInterest - protocolFeeX;

        // get actual interest for lender1 by scaler
        uint256 actualDepositedXInterest =
            lender1Params.depositedXShares * (fixture.pair().exposed_sharesToAssetsScaler(DEPOSIT_X) - RAY) / RAY;

        // get expected interest for LPs
        uint256 expectedInterestForLP = netBorrowXInterest - actualDepositedXInterest;

        // verify reserveX
        (uint112 reserveXIncreasedByInterest,,) = fixture.pair().getReserves();

        assertEq(
            initialLX + user1Params.mintedLX + expectedInterestForLP,
            reserveXIncreasedByInterest,
            'reserveX should match'
        );
    }

    function testInterest_BorrowXAgainstXL_MultiDepositsAndMultiPeriods() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // deposit1
        lender1Params = interestFixture.depositHelper(lender1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        int56 lastLendingCumulativeSum1 = fixture.pair().exposed_observations().lendingCumulativeSum;

        uint256[2] memory durations = [uint256(365 days), uint256(24 hours)];
        // warp and sync to trigger interest accrual for first period

        interestFixture.warpAndSkim(durations[0]);

        uint256 totalBorrowedXAssets;
        uint256 totalDepositedXAndLXAssets;
        uint256 totalBorrowXInterest;
        uint256 borrowInterestInFirstPeriod;
        {
            totalBorrowedXAssets = borrower1Params.borrowedXAssets;

            uint256 depositedXAssets = lender1Params.depositedXAssets;
            totalDepositedXAndLXAssets = getTotalDepositedXAssets(
                depositedXAssets,
                uint112(initialL + user1Params.mintedL),
                ZERO_BORROWED_L_ASSETS,
                lastLendingCumulativeSum1,
                durations[0]
            );

            borrowInterestInFirstPeriod =
                getExpectedInterest(durations[0], totalBorrowedXAssets, totalDepositedXAndLXAssets);
        }

        uint256 firstDepositXScaler = fixture.pair().exposed_sharesToAssetsScaler(DEPOSIT_X);

        // deposit2 and trigger interest accrual for the first period
        uint256 initialLender2DepositAssets = lender2Params.depositedXAssets;

        lender2Params = interestFixture.depositHelper(lender2Params);

        int56 lastLendingCumulativeSum2 = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp and sync to accrue interest for second period
        interestFixture.warpAndSkim(durations[1]);

        uint256 protocolFeeX1 = borrowInterestInFirstPeriod * Interest.LENDING_FEE_RATE / 100;

        {
            // second period interest
            totalBorrowedXAssets = borrower1Params.borrowedXAssets + borrowInterestInFirstPeriod;
            lender1Params.depositedXAssets = lender1Params.depositedXShares * firstDepositXScaler / RAY;
            uint256 depositedXAssets = lender1Params.depositedXAssets + lender2Params.depositedXAssets + protocolFeeX1;

            totalDepositedXAndLXAssets = getTotalDepositedXAssets(
                depositedXAssets,
                uint112(initialL + user1Params.mintedL),
                ZERO_BORROWED_L_ASSETS,
                lastLendingCumulativeSum2,
                durations[1]
            );

            uint256 borrowInterestInSecondPeriod =
                getExpectedInterest(durations[1], totalBorrowedXAssets, totalDepositedXAndLXAssets);

            totalBorrowXInterest = borrowInterestInFirstPeriod + borrowInterestInSecondPeriod;

            assertEq(
                borrower1Params.borrowedXAssets * fixture.pair().exposed_sharesToAssetsScaler(BORROW_X) / RAY,
                borrower1Params.borrowedXAssets + totalBorrowXInterest,
                'BorrowX in Assets mismatch'
            );
        }

        // verify withdrawals
        {
            // withdraw lender1
            lender1Params.withdrawRate = 100;
            assertEq(
                fixture.tokenX().balanceOf(lender1Params.userAddress), 0, 'lender1 should have 0 X before withdraw'
            );
            interestFixture.withdrawHelper(lender1Params);
            assertEq(
                fixture.pair().tokens(DEPOSIT_X).balanceOf(lender1Params.userAddress),
                0,
                'lender1 should have some depositX after withdraw'
            );

            uint256 expectedWithdrawX1 =
                lender1Params.depositedXShares * fixture.pair().exposed_sharesToAssetsScaler(DEPOSIT_X) / RAY;
            assertApproxEqRel(
                fixture.tokenX().balanceOf(lender1Params.userAddress),
                expectedWithdrawX1,
                TOLERANCE_1,
                'lender1 withdrawX mismatch'
            );

            // withdraw lender2
            lender2Params.withdrawRate = 100;
            assertEq(
                fixture.tokenX().balanceOf(lender2Params.userAddress), 0, 'lender2 should have 0 X before withdraw'
            );

            uint256 expectedWithdrawX2InAsset =
                lender2Params.depositedXShares * fixture.computeScalerHelper(DEPOSIT_X) / RAY;
            assertGt(expectedWithdrawX2InAsset, initialLender2DepositAssets, 'lender2 withdrawX should be greater');
            assertGt(fixture.computeScalerHelper(DEPOSIT_X), RAY, 'depositX scaler should be > RAY');

            interestFixture.withdrawHelper(lender2Params);
            assertEq(
                fixture.pair().tokens(DEPOSIT_X).balanceOf(lender2Params.userAddress),
                0,
                'lender2 should have some depositX after withdraw'
            );

            assertEq(
                fixture.tokenX().balanceOf(lender2Params.userAddress),
                expectedWithdrawX2InAsset,
                'lender2 withdrawX should match'
            );
        }
    }

    function getTotalDepositedXAssets(
        uint256 depositedXAssets,
        uint112 depositedLAssets,
        uint112 borrowedLAssets,
        int56 lastLendingCumulativeSum,
        uint256 duration
    ) private view returns (uint256) {
        int16 lendingStateTick = fixture.pair().exposed_getLendingStateTick(lastLendingCumulativeSum, duration);

        (uint256 reserveXAssetsAtLendingTick,) =
            Interest.getReservesAtTick(depositedLAssets - borrowedLAssets, lendingStateTick);

        return reserveXAssetsAtLendingTick + depositedXAssets;
    }

    function testInterest_BorrowXAgainstXL_TriggeredByWithdraw() public {
        // lender1 deposit
        lender1Params = interestFixture.depositHelper(lender1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        // warp and sync
        uint256 duration = 1 days;
        interestFixture.warpForwardBy(duration);

        // compute interest

        assertGt(fixture.computeScalerHelper(DEPOSIT_X), RAY, 'depositX scaler should be greater than RAY');

        // withdraw lender1
        lender1Params.withdrawRate = 100;

        uint256 expectedWithdrawX = lender1Params.depositedXShares * fixture.pair().totalAssets()[DEPOSIT_X]
            / fixture.pair().tokens(DEPOSIT_X).totalSupply();

        interestFixture.withdrawHelper(lender1Params);

        assertEq(
            fixture.pair().tokens(BORROW_X).balanceOf(lender1Params.userAddress),
            0,
            'lender1 BORROW_X balance should be 0 after lender1 withdrawX'
        );

        uint256 actualWithdrawX = fixture.tokenX().balanceOf(lender1Params.userAddress);

        assertEq(actualWithdrawX, expectedWithdrawX, 'lender1Params actual withdrawX should match expected withdrawX');
    }

    function testInterest_BorrowXAgainstL_OneBorrower_OneMintAndBurnAndCollectInterest_RepayAfterBurn() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp and sync
        uint256 duration = 365 days;
        interestFixture.warpAndSkim(duration);

        // compute interest
        uint256 totalBorrowedXAssets = borrower1Params.borrowedXAssets;
        uint256 totalDepositedXAssets =
            getTotalDepositedAssets(ZERO_DEPOSITED_X_ASSETS, lastLendingCumulativeSum, duration);

        uint256 totalBorrowXInterest = getExpectedInterest(duration, totalBorrowedXAssets, totalDepositedXAssets);
        uint256 protocolFeeX = totalBorrowXInterest * Interest.LENDING_FEE_RATE / 100;
        uint256 netBorrowXInterest = totalBorrowXInterest - protocolFeeX;

        uint112 reserveXWithInterest = uint112(initialLX + user1Params.mintedLX) + uint112(netBorrowXInterest);

        // Note: get activeLiquidityAssets before burns to be used computing expected burn amount.
        (,, uint256 activeLiquidityAssetsBeforeBurns) =
            fixture.pair().exposed_getDepositAndBorrowAndActiveLiquidityAssets();

        // minter1 burn
        user1Params.burnRate = 100;
        user1Params = interestFixture.burnHelper(user1Params);

        // verify burn results
        assertEq(
            fixture.pair().tokens(DEPOSIT_L).balanceOf(user1Params.userAddress),
            0,
            'Liquidity balance should not be greater than 0 after user1 burnX'
        );

        uint256 user1ActualBurnedX = fixture.tokenX().balanceOf(user1Params.userAddress);
        uint256 user1ExpectedBurnedX = user1Params.burnedL * reserveXWithInterest / activeLiquidityAssetsBeforeBurns;

        assertEq(user1ActualBurnedX, user1ExpectedBurnedX, 'user1Params burnX amount should match');

        // repay borrow1
        borrower1Params.repayRate = 100;
        interestFixture.repayHelper(borrower1Params);

        assertEq(
            fixture.pair().tokens(BORROW_X).balanceOf(borrower1Params.userAddress),
            0,
            'borrower1 BORROW_X balance should be 0 after borrower1 repayX'
        );
    }

    function testInterest_BorrowXAgainstL_TriggeredByRepay() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp without triggering interest accrual
        uint256 duration = 1 days;
        interestFixture.warpAndSkim(duration);

        // compute interest
        getTotalDepositedAssets(ZERO_DEPOSITED_X_ASSETS, lastLendingCumulativeSum, duration);

        borrower1Params.borrowedXShares = fixture.pair().tokens(BORROW_X).balanceOf(borrower1Params.userAddress);
        uint256 borrowXScaler = fixture.pair().exposed_sharesToAssetsScaler(BORROW_X);

        // repay borrow1 where the assets are calculated by getTotalAssets
        borrower1Params.repayRate = 100;
        (uint256 repaidXByConvertToAssets,) = interestFixture.repayAssetsHelper(borrower1Params);

        /**
         * The repaidXByScaler calculation is based on the updated v5 _convertToAssets function
         * ref: https://github.com/OpenZeppelin/openzeppelin-contracts/blob/69c8def5f222ff96f2b5beff05dfba996368aa79/contracts/token/ERC20/extensions/ERC4626.sol#L232
         * The logic adds 1 totalAssets and add 10 ** _decimalsOffset() to totalSupply(), _decimalsOffset() returns 0 if not overriden
         * which means it effective evaluates to 10 ** 0 = 1.
         *
         * The replayAssetsHelper function calls convertToAsset without passing in a rounding bias: ammXToken.convertToAssets(repayXShares)
         *
         * Therefore the following calculation is adjusted to reflect the new logic matching the upgraded v5 _convertToAssets function in ERC4626
         */
        uint256 repaidXAssetsFromScaler = borrower1Params.borrowedXShares * borrowXScaler / RAY;

        assertEq(
            repaidXByConvertToAssets, repaidXAssetsFromScaler, 'repaidXByConvertToAssets should match repaidXByScaler'
        );

        assertApproxEqAbs(
            fixture.pair().tokens(BORROW_X).balanceOf(borrower1Params.userAddress),
            0,
            1,
            'borrower1 BORROW_X balance should be 0 after borrower1 repayX'
        );
    }

    function testBorrowXProtocolFee() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp and sync
        uint256 duration = 365 days;
        interestFixture.warpAndSkim(duration);

        // compute interest
        uint256 totalBorrowedXAssets = borrower1Params.borrowedXAssets;
        uint256 totalDepositedXAssets = getTotalDepositedXAssets(
            ZERO_DEPOSITED_X_ASSETS,
            uint112(initialL + user1Params.mintedL),
            ZERO_BORROWED_L_ASSETS,
            lastLendingCumulativeSum,
            duration
        );

        uint256 totalBorrowXInterest = getExpectedInterest(duration, totalBorrowedXAssets, totalDepositedXAssets);

        uint256 expectProtocolFeeAssets1 = protocolFee(totalBorrowXInterest);

        uint256 currentDepositXScaler = fixture.computeScalerHelper(DEPOSIT_X);
        uint256 expectProtocolFeeShares1 = expectProtocolFeeAssets1 * RAY / currentDepositXScaler;

        uint256 actualProtocolFeeShares1 = fixture.pair().tokens(DEPOSIT_X).balanceOf(fixture.factory().feeTo());

        assertGt(actualProtocolFeeShares1, 0, 'protocol fee shares should be greater than 0');

        assertEq(actualProtocolFeeShares1, expectProtocolFeeShares1, 'protocol fee in DEPOSIT_X should match');

        // below is to check second period interest and protocol fee, duration is changed to 182.5 days
        duration = 182.5 days;

        int56 lastLendingCumulativeSum2 = fixture.pair().exposed_observations().lendingCumulativeSum;

        interestFixture.warpAndSkim(duration);

        // compute interest
        uint256 totalBorrowedXAssets2 = totalBorrowedXAssets + totalBorrowXInterest;
        uint256 totalDepositedXAssets2 = getTotalDepositedXAssets(
            expectProtocolFeeAssets1,
            uint112(initialL + user1Params.mintedL),
            ZERO_BORROWED_L_ASSETS,
            lastLendingCumulativeSum2,
            duration
        );

        uint256 totalBorrowXInterest2 = getExpectedInterest(duration, totalBorrowedXAssets2, totalDepositedXAssets2);

        uint256 actualTotalProtocolFeeShares = fixture.pair().tokens(DEPOSIT_X).balanceOf(fixture.factory().feeTo());

        assertGt(
            actualTotalProtocolFeeShares,
            actualProtocolFeeShares1,
            'protocol fee shares should be greater than prev one'
        );

        uint256 expectedProtocolFeeAssets2 = totalBorrowXInterest2 * Interest.LENDING_FEE_RATE / 100;

        uint256 currentDepositXScaler2 = fixture.computeScalerHelper(DEPOSIT_X);
        uint256 expectedProtocolFeeShares2 = expectedProtocolFeeAssets2 * RAY / currentDepositXScaler2;

        assertEq(
            actualTotalProtocolFeeShares,
            expectedProtocolFeeShares2 + expectProtocolFeeShares1,
            'protocol fee2 in DEPOSIT_Y should match'
        );
    }

    function protocolFee(
        uint256 interest
    ) public pure returns (uint256) {
        return interest * Interest.LENDING_FEE_RATE / 100;
    }
}
