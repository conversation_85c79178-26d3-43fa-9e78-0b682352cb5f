// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.18;

import {Test} from 'forge-std/Test.sol';

import {DEFAULT_MID_TERM_INTERVAL} from 'contracts/libraries/constants.sol';
import {GeometricTWAP} from 'contracts/libraries/GeometricTWAP.sol';
import {MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG} from 'contracts/libraries/constants.sol';
import {
    GeometricTWAPTestFixture,
    DEFAULT_LONG_TERM_INTERVAL,
    DEFAULT_TICK_VALUE
} from 'test/shared/GeometricTWAPTestFixture.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';

/**
 * @notice  Fuzz tests for GeometricTWAPSpec.
 * @dev     This contract contains two fuzz tests for GeometricTWAPSpec.
 *          1. The first test is to test the observation index when there are missing blocks.
 *          2. The second test is to test the ticks when there is a new different tick at the last block.
 */
contract GeometricTWAPSpecFuzzTests is Test {
    GeometricTWAPTestFixture private fixture;

    /**
     * @notice  Foundry In-line fuzz test configuration. Place this directly above the function for the local config to take effect.
     *          forge-config: default.fuzz.runs = 50
     * @dev     The purpose of this fuzz test is to verify the long and mid-term tick range with a wide random range for any patterns of
     *          blocks, blocks to miss, the longTermIntervalConfig and with any number of past blocks.
     * @param   firstBlocks                     The first portion of active blocks being observed.
     * @param   missingBlocks                   The number of blocks to be intentionally missed for testing.
     * @param   secondBlocks                    The second portion of active blocks being observed after the missing blocks.
     * @param   longTermIntervalConfigFactor    The initial configuration value for long-term intervals.
     */
    function testGeometricTWAPFuzz_ObserveIndexWithMissingBlocks(
        uint24 firstBlocks,
        uint24 missingBlocks,
        uint24 secondBlocks,
        uint24 longTermIntervalConfigFactor
    ) public {
        longTermIntervalConfigFactor = uint24(bound(longTermIntervalConfigFactor, 1, DEFAULT_LONG_TERM_INTERVAL));

        uint256 maxBlockBound = 1000; // smaller number to prevent excessively forge test running times.
        firstBlocks = uint24(bound(firstBlocks, DEFAULT_LONG_TERM_INTERVAL * 9, maxBlockBound));
        secondBlocks = uint24(bound(secondBlocks, DEFAULT_LONG_TERM_INTERVAL * 9, maxBlockBound));

        uint256 activeBlocks = firstBlocks + secondBlocks;
        uint256 longTermIntervalConfig =
            DEFAULT_MID_TERM_INTERVAL * longTermIntervalConfigFactor * GeometricTWAP.MID_TERM_ARRAY_LENGTH;
        uint256 minimumBlocksForLongTermBuffer = longTermIntervalConfig * GeometricTWAP.LONG_TERM_ARRAY_LENGTH;

        if (activeBlocks < minimumBlocksForLongTermBuffer) {
            // To avoid `AmmalgamLongTermBufferIsNotInitialized` error.
            secondBlocks += uint24(minimumBlocksForLongTermBuffer - activeBlocks);
        }

        // Note: This bound for missingBlocks is to avoid issues when casting uint24 to int24 in the library function getObservedTicks(),
        // ensure that the number of missing blocks is within a safe range.
        // For instance, int24(8388608) becomes -8388608, potentially causing negative long-term tick values.
        missingBlocks = uint24(
            bound(
                missingBlocks,
                0,
                uint256(uint24(type(int24).max)) - longTermIntervalConfig * (GeometricTWAP.LONG_TERM_ARRAY_LENGTH - 1)
            )
        );

        verifyObservedIndex(
            firstBlocks,
            missingBlocks,
            secondBlocks,
            DEFAULT_MID_TERM_INTERVAL,
            longTermIntervalConfigFactor * MINIMUM_LONG_TERM_TIME_UPDATE_CONFIG
        );
    }

    function verifyObservedIndex(
        uint256 firstActiveBlocks,
        uint24 missingAfterFirstActiveBlocks,
        uint256 secondActiveBlocks,
        uint24 midTermIntervalConfig,
        uint24 longTermIntervalConfigFactor
    ) private {
        fixture = new GeometricTWAPTestFixture(midTermIntervalConfig, longTermIntervalConfigFactor, DEFAULT_TICK_VALUE);

        fixture.generateObservations(firstActiveBlocks, DEFAULT_TICK_VALUE);
        fixture.mineBlock(missingAfterFirstActiveBlocks);
        fixture.generateObservations(secondActiveBlocks, DEFAULT_TICK_VALUE);

        fixture.verifyTicks(DEFAULT_TICK_VALUE);
    }
}
