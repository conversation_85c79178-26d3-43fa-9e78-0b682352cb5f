// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';

import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';
import {IERC4626} from '@openzeppelin/contracts/interfaces/IERC4626.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {MathLib, WAD} from '@morpho-org/morpho-blue/src/libraries/MathLib.sol';

import {DEPOSIT_L, DEPOSIT_Y, BORROW_L, BORROW_Y} from 'contracts/interfaces/tokens/ITokenController.sol';
import {Interest} from 'contracts/libraries/Interest.sol';

import {Validation} from 'contracts/libraries/Validation.sol';

import {FactoryPairTestFixture, MAX_TOKEN, IPairHarness} from 'test/shared/FactoryPairTestFixture.sol';
import {
    InterestFixture,
    MintAndBurnInputParams,
    BorrowAndRepayXYInputParams,
    DepositAndWithdrawInputParams,
    RAY
} from 'test/InterestTests/InterestFixture.sol';

import {getExpectedInterest} from 'test/shared/utilities.sol';

contract InterestYIntegrationTests is Test {
    using MathLib for uint256;

    uint112 constant ZERO_DEPOSITED_Y_ASSETS = 0;
    uint112 constant ZERO_BORROWED_L_ASSETS = 0;

    FactoryPairTestFixture public fixture;
    InterestFixture public interestFixture;

    address random = address(0xa0);
    address borrower1Addr = address(0xb1);
    address borrower2Addr = address(0xb2);
    address minter1Addr = address(0xd1);
    address minter2Addr = address(0xd2);
    address lender1Addr = address(0xc1);
    address lender2Addr = address(0xc2);

    uint256 totalBShares;
    uint256 totalDShares;

    uint256 initialLX;
    uint256 initialLY;
    uint256 initialL;

    MintAndBurnInputParams user1Params;
    MintAndBurnInputParams user2Params;
    DepositAndWithdrawInputParams lender1Params;
    DepositAndWithdrawInputParams lender2Params;
    BorrowAndRepayXYInputParams borrower1Params;

    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        interestFixture = new InterestFixture(fixture);

        initPool(100e18, 50e18);

        setUpUsers();
    }

    function initPool(uint256 _initialLX, uint256 _initialLY) private {
        fixture.transferTokensTo(random, _initialLX, _initialLY);
        initialL = fixture.mintForAndInitializeBlocks(random, _initialLX, _initialLY);

        initialLX = _initialLX;
        initialLY = _initialLY;
    }

    function setUpUsers() private {
        user1Params.userAddress = minter1Addr;
        user1Params.mintedLX = 200e18;
        user1Params.mintedLY = 100e18;

        user2Params.userAddress = minter2Addr;
        user2Params.mintedLX = 300e18;
        user2Params.mintedLY = 150e18;

        lender1Params.userAddress = lender1Addr;
        lender1Params.depositedYAssets = 150e18;

        lender2Params.userAddress = lender2Addr;
        lender2Params.depositedYAssets = 2000e18; // will be recalculated after deposit

        borrower1Params.userAddress = borrower1Addr;
        borrower1Params.borrowedYAssets = 13e18; // picked number to be easier diff
        borrower1Params.collateralX = type(uint112).max;
    }

    function testInterest_BorrowYAgainstL_ReserveIncreased() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp and sync
        uint256 duration = 365 days;
        interestFixture.warpAndSkim(duration);

        // compute interest
        uint256 totalBorrowedYAssets = borrower1Params.borrowedYAssets;

        uint112 depositedLAssets = uint112(initialL + user1Params.mintedL);
        uint256 totalDepositedYAssets = getTotalDepositedYAssets(
            ZERO_DEPOSITED_Y_ASSETS, depositedLAssets, ZERO_BORROWED_L_ASSETS, lastLendingCumulativeSum, duration
        );

        uint256 totalBorrowYInterest = getExpectedInterest(duration, totalBorrowedYAssets, totalDepositedYAssets);
        uint256 protocolFeeY = totalBorrowYInterest * Interest.LENDING_FEE_RATE / 100;
        uint256 netBorrowYInterest = totalBorrowYInterest - protocolFeeY;

        // verify reserveY
        (, uint112 reserveY,) = fixture.pair().getReserves();

        assertEq(
            reserveY,
            uint112(initialLY + user1Params.mintedLY + netBorrowYInterest),
            'reserveY should match after interest accrual'
        );
    }

    function testInterest_BorrowYAgainstL_OneBorrower_OneMintAndBurnAndCollectInterest() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp and sync
        uint256 duration = 365 days;
        interestFixture.warpAndSkim(duration);

        // compute interest
        uint256 totalBorrowedYAssets = borrower1Params.borrowedYAssets;

        uint112 depositedLAssets = uint112(initialL + user1Params.mintedL);
        uint256 totalDepositedYAssets = getTotalDepositedYAssets(
            ZERO_DEPOSITED_Y_ASSETS, depositedLAssets, ZERO_BORROWED_L_ASSETS, lastLendingCumulativeSum, duration
        );

        uint256 totalBorrowYInterest = getExpectedInterest(duration, totalBorrowedYAssets, totalDepositedYAssets);
        uint256 protocolFeeY = totalBorrowYInterest * Interest.LENDING_FEE_RATE / 100;
        uint256 netBorrowYInterest = totalBorrowYInterest - protocolFeeY;

        uint112 reserveYWithInterest = uint112(initialLY + user1Params.mintedLY) + uint112(netBorrowYInterest);

        // Note: get activeLiquidityAssets before burns to be used computing expected burn amount.
        (,, uint256 activeLiquidityAssetsBeforeBurns) =
            fixture.pair().exposed_getDepositAndBorrowAndActiveLiquidityAssets();

        // minter1 burn
        user1Params.burnRate = 100;
        user1Params = interestFixture.burnHelper(user1Params);

        // verify burn results
        assertEq(
            fixture.pair().tokens(DEPOSIT_L).balanceOf(user1Params.userAddress),
            0,
            'Liquidity balance should be 0 after user1 burnY'
        );

        uint256 user1ActualBurnedY = fixture.tokenY().balanceOf(user1Params.userAddress);
        uint256 user1ExpectedBurnedY = user1Params.burnedL * reserveYWithInterest / activeLiquidityAssetsBeforeBurns;

        assertEq(user1ActualBurnedY, user1ExpectedBurnedY, 'user1Params burnY amount should match');
    }

    function testInterest_BorrowYAgainstL_TriggeredByMint() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp without sync
        uint256 duration = 365 days;
        interestFixture.warpForwardBy(duration);

        // Interest accrual is triggered by User2's minting action instead of sync
        user2Params = interestFixture.mintHelper(user2Params);

        // compute interest
        uint256 totalBorrowedYAssets = borrower1Params.borrowedYAssets;

        uint112 depositedLAssets = uint112(initialL + user1Params.mintedL);
        uint256 totalDepositedYAssets = getTotalDepositedYAssets(
            ZERO_DEPOSITED_Y_ASSETS, depositedLAssets, ZERO_BORROWED_L_ASSETS, lastLendingCumulativeSum, duration
        );

        uint256 totalBorrowYInterest = getExpectedInterest(duration, totalBorrowedYAssets, totalDepositedYAssets);
        uint256 protocolFeeY = totalBorrowYInterest * Interest.LENDING_FEE_RATE / 100;
        uint256 netBorrowYInterest = totalBorrowYInterest - protocolFeeY;

        // verify reserveY
        (, uint112 reserveY2,) = fixture.pair().getReserves();

        assertEq(
            initialLY + user1Params.mintedLY + user2Params.mintedLY + netBorrowYInterest,
            reserveY2,
            'reserveY should match after user2 mint'
        );
    }

    function testInterest_BorrowY_CanSkimStealInterestForY() public {
        user1Params = interestFixture.mintHelper(user1Params);
        interestFixture.borrowHelper(borrower1Params);
        interestFixture.warpForwardBy(1 days);

        IERC20 tokenX = fixture.tokenX();
        IERC20 tokenY = fixture.tokenY();

        uint256 tokenXBalance = tokenX.balanceOf(address(fixture.pair()));
        uint256 tokenYBalance = tokenY.balanceOf(address(fixture.pair()));

        address addressThis = address(this);

        assertEq(tokenX.balanceOf(addressThis), 0, 'tokenX balance should be 0');
        assertEq(tokenY.balanceOf(addressThis), 0, 'tokenY balance should be 0');

        fixture.pair().skim(addressThis);

        assertEq(tokenX.balanceOf(address(fixture.pair())), tokenXBalance, 'tokenX balance should not change');
        assertEq(tokenY.balanceOf(address(fixture.pair())), tokenYBalance, 'tokenY balance should not change');

        assertEq(tokenX.balanceOf(addressThis), 0, 'tokenX balance should be 0');
        assertEq(tokenY.balanceOf(addressThis), 0, 'tokenY balance should be 0');
    }

    function testInterest_BorrowYAgainstL_TriggeredByMinter2AndCollectingInterest() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp without sync
        uint256[2] memory durations = [uint256(365 days), uint256(24 hours)];
        interestFixture.warpForwardBy(durations[0]);

        // Interest accrual from period1 is triggered by User2's minting action instead of sync
        user2Params = interestFixture.mintHelper(user2Params);

        // compute interest from period1
        uint256 totalBorrowedYAssets = borrower1Params.borrowedYAssets;

        uint112 depositedLAssets = uint112(initialL + user1Params.mintedL);
        uint256 totalDepositedYAssets = getTotalDepositedYAssets(
            ZERO_DEPOSITED_Y_ASSETS, depositedLAssets, ZERO_BORROWED_L_ASSETS, lastLendingCumulativeSum, durations[0]
        );

        uint256 borrowInterestInFirstPeriod =
            getExpectedInterest(durations[0], totalBorrowedYAssets, totalDepositedYAssets);
        uint256 protocolFeeYFirstPeriod = borrowInterestInFirstPeriod * Interest.LENDING_FEE_RATE / 100;

        lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp and sync to accrue interest from period2
        interestFixture.warpAndSkim(durations[1]);

        // compute interest from period2
        totalBorrowedYAssets = borrower1Params.borrowedYAssets + borrowInterestInFirstPeriod;

        depositedLAssets = uint112(initialL + user1Params.mintedL + user2Params.mintedL);
        totalDepositedYAssets = getTotalDepositedYAssets(
            protocolFeeYFirstPeriod, depositedLAssets, ZERO_BORROWED_L_ASSETS, lastLendingCumulativeSum, durations[1]
        );

        // verify reserveY
        (, uint112 reserveYSecondPeriod,) = fixture.pair().getReserves();

        // Note: get activeLiquidityAssets before burns to be used computing expected burn amount.
        (,, uint256 activeLiquidityAssetsBeforeBurns) =
            fixture.pair().exposed_getDepositAndBorrowAndActiveLiquidityAssets();

        // minter1 burn
        user1Params.burnRate = 100;
        user1Params = interestFixture.burnHelper(user1Params);

        // verify burn results
        assertEq(
            fixture.pair().tokens(DEPOSIT_L).balanceOf(user1Params.userAddress),
            0,
            'Liquidity balance should be 0 after user1 burnY'
        );

        uint256 user1ActualBurnedY = fixture.tokenY().balanceOf(user1Params.userAddress);
        uint256 user1ExpectedBurnedY = user1Params.burnedL * reserveYSecondPeriod / activeLiquidityAssetsBeforeBurns;

        assertEq(user1ActualBurnedY, user1ExpectedBurnedY, 'user1Params burnY amount should match');

        (, uint112 reserveYAfterFirstUserBurn,) = fixture.pair().getReserves();
        // Note: get activeLiquidityAssets before burns to be used computing expected burn amount.
        (,, activeLiquidityAssetsBeforeBurns) = fixture.pair().exposed_getDepositAndBorrowAndActiveLiquidityAssets();

        // minter2 burn
        user2Params.burnRate = 100;
        user2Params = interestFixture.burnHelper(user2Params);

        // verify burn results
        assertEq(
            fixture.pair().tokens(DEPOSIT_L).balanceOf(user2Params.userAddress),
            0,
            'Liquidity balance should be 0 after user2 burnY'
        );

        uint256 user2ActualBurnedY = fixture.tokenY().balanceOf(user2Params.userAddress);
        uint256 user2ExpectedBurnedY =
            user2Params.burnedL * reserveYAfterFirstUserBurn / activeLiquidityAssetsBeforeBurns;

        assertEq(user2ActualBurnedY, user2ExpectedBurnedY, 'user2Params burnY amount should match');
    }

    function testInterest_BorrowYAgainstL_User2MintTriggerInterestButAlsoCollectingInterest() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        uint256[2] memory durations = [uint256(365 days), uint256(24 hours)];

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;
        // warp without accrue interest
        interestFixture.warpForwardBy(durations[0]);
        (uint112 reserveX0, uint112 reserveY0,) = fixture.pair().getReserves();

        // Note: user2 mintedLX is calculated by user2's mintedLY due to interest has been added up on reserveY
        user2Params.mintedLX = user2Params.mintedLY * reserveX0 / reserveY0;
        user2Params = interestFixture.mintHelper(user2Params); // mint to trigger interest accrual for period 1

        // compute interest from period1 triggered by mint
        uint256 totalBorrowedYAssets = borrower1Params.borrowedYAssets;

        uint112 depositedLAssets = uint112(initialL + user1Params.mintedL);
        uint256 totalDepositedYAssets = getTotalDepositedYAssets(
            ZERO_DEPOSITED_Y_ASSETS, depositedLAssets, ZERO_BORROWED_L_ASSETS, lastLendingCumulativeSum, durations[0]
        );

        uint256 borrowInterestInFirstPeriod =
            getExpectedInterest(durations[0], totalBorrowedYAssets, totalDepositedYAssets);

        lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;
        // warp and sync to accrue interest
        interestFixture.warpAndSkim(durations[1]);

        totalBorrowedYAssets = borrower1Params.borrowedYAssets + borrowInterestInFirstPeriod;

        depositedLAssets = uint112(initialL + user1Params.mintedL + user2Params.mintedL);
        totalDepositedYAssets = getTotalDepositedYAssets(
            ZERO_DEPOSITED_Y_ASSETS, depositedLAssets, ZERO_BORROWED_L_ASSETS, lastLendingCumulativeSum, durations[1]
        );

        // verify reserveY
        (, uint112 reserveY2,) = fixture.pair().getReserves();

        // Note: get activeLiquidityAssets before burns to be used computing expected burn amount.
        (,, uint256 activeLiquidityAssetsBeforeBurns) =
            fixture.pair().exposed_getDepositAndBorrowAndActiveLiquidityAssets();

        // minter1 burn
        user1Params.burnRate = 100;
        user1Params = interestFixture.burnHelper(user1Params);

        (, uint112 reserveYAfterUser1Burn,) = fixture.pair().getReserves();

        // verify burn results
        uint256 user1ActualBurnedY = fixture.tokenY().balanceOf(user1Params.userAddress);
        uint256 user1ExpectedBurnedY = user1Params.burnedL * reserveY2 / activeLiquidityAssetsBeforeBurns;
        assertEq(
            fixture.pair().tokens(DEPOSIT_L).balanceOf(user1Params.userAddress),
            0,
            'Liquidity balance should be 0 after user1 burnY'
        );
        assertEq(user1ActualBurnedY, user1ExpectedBurnedY, 'user1Params burnY amount should match');

        // Note: get activeLiquidityAssets before burns to be used computing expected burn amount.
        (,, activeLiquidityAssetsBeforeBurns) = fixture.pair().exposed_getDepositAndBorrowAndActiveLiquidityAssets();

        // minter2 burn
        user2Params.burnRate = 100;
        user2Params = interestFixture.burnHelper(user2Params);

        // verify burn results
        uint256 user2ActualBurnedY = fixture.tokenY().balanceOf(user2Params.userAddress);
        uint256 user2ExpectedBurnedY = user2Params.burnedL * reserveYAfterUser1Burn / activeLiquidityAssetsBeforeBurns;

        assertEq(
            fixture.pair().tokens(DEPOSIT_L).balanceOf(user2Params.userAddress),
            0,
            'Liquidity balance should be 0 after user2 burnY'
        );
        assertEq(user2ActualBurnedY, user2ExpectedBurnedY, 'user2Params burnY amount should match');
    }

    function testInterest_BorrowYAgainstYL_SingleDepositAndBorrow() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // deposit1
        lender1Params = interestFixture.depositHelper(lender1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp without sync
        uint256 duration = uint256(365 days);
        interestFixture.warpAndSkim(duration);

        // compute expected interest for borrowY
        uint256 totalBorrowedYAssets = borrower1Params.borrowedYAssets;
        uint256 depositedYAssets = lender1Params.depositedYShares; // initially the shares is assets

        uint256 totalDepositedYAssets = getTotalDepositedYAssets(
            depositedYAssets,
            uint112(initialL + user1Params.mintedL),
            ZERO_BORROWED_L_ASSETS,
            lastLendingCumulativeSum,
            duration
        );

        uint256 totalBorrowYInterest = getExpectedInterest(duration, totalBorrowedYAssets, totalDepositedYAssets);
        uint256 protocolFeeY = totalBorrowYInterest * Interest.LENDING_FEE_RATE / 100;
        uint256 netBorrowYInterest = totalBorrowYInterest - protocolFeeY;

        // get actual interest for lender1 by scaler
        uint256 actualDepositedYInterest =
            lender1Params.depositedYShares * (fixture.pair().exposed_sharesToAssetsScaler(DEPOSIT_Y) - RAY) / RAY;

        // get expected interest for LPs
        uint256 expectedInterestForLP = netBorrowYInterest - actualDepositedYInterest;

        // verify reserveY
        (, uint112 reserveYIncreasedByInterest,) = fixture.pair().getReserves();

        assertEq(
            initialLY + user1Params.mintedLY + expectedInterestForLP,
            reserveYIncreasedByInterest,
            'reserveY should match'
        );
    }

    function getTotalDepositedYAssets(
        uint256 depositedYAssets,
        uint112 depositedLAssets,
        uint112 borrowedLAssets,
        int56 lastLendingCumulativeSum,
        uint256 duration
    ) private view returns (uint256) {
        int16 lendingStateTick = fixture.pair().exposed_getLendingStateTick(lastLendingCumulativeSum, duration);

        (, uint256 reserveYAssetsAtLendingTick) =
            Interest.getReservesAtTick(depositedLAssets - borrowedLAssets, lendingStateTick);

        return reserveYAssetsAtLendingTick + depositedYAssets;
    }

    function testBorrowYProtocolFee() public {
        // minter1 mint
        user1Params = interestFixture.mintHelper(user1Params);

        // borrow1
        interestFixture.borrowHelper(borrower1Params);

        int56 lastLendingCumulativeSum = fixture.pair().exposed_observations().lendingCumulativeSum;

        // warp and sync
        uint256 duration = 365 days;
        interestFixture.warpAndSkim(duration);

        // compute interest
        uint256 totalBorrowedYAssets = borrower1Params.borrowedYAssets;
        uint256 totalDepositedYAssets = getTotalDepositedYAssets(
            ZERO_DEPOSITED_Y_ASSETS,
            uint112(initialL + user1Params.mintedL),
            ZERO_BORROWED_L_ASSETS,
            lastLendingCumulativeSum,
            duration
        );

        uint256 totalBorrowYInterest = getExpectedInterest(duration, totalBorrowedYAssets, totalDepositedYAssets);

        uint256 expectProtocolFeeAssets1 = protocolFee(totalBorrowYInterest);

        uint256 currentDepositYScaler = fixture.computeScalerHelper(DEPOSIT_Y);
        uint256 expectProtocolFeeShares1 = expectProtocolFeeAssets1 * RAY / currentDepositYScaler;

        uint256 actualProtocolFeeShares1 = fixture.pair().tokens(DEPOSIT_Y).balanceOf(fixture.factory().feeTo());

        assertGt(actualProtocolFeeShares1, 0, 'protocol fee shares should be greater than 0');

        assertEq(actualProtocolFeeShares1, expectProtocolFeeShares1, 'protocol fee in DEPOSIT_Y should match');

        // below is to check second period interest and protocol fee, duration is changed to 182.5 days
        duration = 182.5 days;

        int56 lastLendingCumulativeSum2 = fixture.pair().exposed_observations().lendingCumulativeSum;

        interestFixture.warpAndSkim(duration);

        // compute interest
        uint256 totalBorrowedYAssets2 = totalBorrowedYAssets + totalBorrowYInterest;
        uint256 totalDepositedYAssets2 = getTotalDepositedYAssets(
            expectProtocolFeeAssets1,
            uint112(initialL + user1Params.mintedL),
            ZERO_BORROWED_L_ASSETS,
            lastLendingCumulativeSum2,
            duration
        );

        uint256 totalBorrowYInterest2 = getExpectedInterest(duration, totalBorrowedYAssets2, totalDepositedYAssets2);

        uint256 actualTotalProtocolFeeShares = fixture.pair().tokens(DEPOSIT_Y).balanceOf(fixture.factory().feeTo());

        assertGt(
            actualTotalProtocolFeeShares,
            actualProtocolFeeShares1,
            'protocol fee shares should be greater than prev one'
        );

        uint256 expectedProtocolFeeAssets2 = totalBorrowYInterest2 * Interest.LENDING_FEE_RATE / 100;

        uint256 currentDepositYScaler2 = fixture.computeScalerHelper(DEPOSIT_Y);
        uint256 expectedProtocolFeeShares2 = expectedProtocolFeeAssets2 * RAY / currentDepositYScaler2;

        assertEq(
            actualTotalProtocolFeeShares,
            expectedProtocolFeeShares2 + expectProtocolFeeShares1,
            'protocol fee2 in DEPOSIT_Y should match'
        );
    }

    function protocolFee(
        uint256 interest
    ) public pure returns (uint256) {
        return interest * Interest.LENDING_FEE_RATE / 100;
    }
}
