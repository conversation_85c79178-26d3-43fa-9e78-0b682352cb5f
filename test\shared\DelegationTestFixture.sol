// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Vm} from 'forge-std/Vm.sol';

import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {IERC20DebtToken} from 'contracts/interfaces/tokens/IERC20DebtToken.sol';
import {
    IDebtDelegation, PeripheralDelegationContractExample
} from 'test/example/PeripheralDelegationContractExample.sol';

contract DelegationTestFixture is Test {
    IAmmalgamPair private pair;
    IDebtDelegation public delegationContract;

    constructor(
        IAmmalgamPair _pair
    ) {
        pair = _pair;
        delegationContract = new PeripheralDelegationContractExample();
    }

    function getDebtToken(
        uint256 tokenType
    ) public view returns (IERC20DebtToken) {
        return IERC20DebtToken(address(pair.tokens(tokenType)));
    }

    function approveDebtTransferAndDelegation(
        address onBehalfOf,
        address delegatee,
        uint256 tokenType,
        uint256 approveAmount
    ) public {
        IERC20DebtToken debtToken = getDebtToken(tokenType);

        vm.startPrank(onBehalfOf);
        debtToken.approveDebt(address(delegationContract), approveAmount);
        delegationContract.approveDelegation(delegatee);
        vm.stopPrank();
    }

    function delegateBorrow(
        address onBehalfOf,
        address delegatee,
        address to,
        uint256 amountX,
        uint256 amountY
    ) public {
        vm.prank(delegatee);
        delegationContract.borrow(onBehalfOf, to, pair, amountX, amountY);
    }

    function delegateBorrowLiquidity(address onBehalfOf, address delegatee, address to, uint256 amount) public {
        vm.prank(delegatee);
        delegationContract.borrowLiquidity(onBehalfOf, to, pair, amount);
    }

    function verifyDelegationContractIsEmpty(
        uint256 tokenType
    ) public view {
        IERC20DebtToken debtToken = getDebtToken(tokenType);
        assertEq(debtToken.balanceOf(address(delegationContract)), 0, 'Delegation contract should not have debt');
    }
}
