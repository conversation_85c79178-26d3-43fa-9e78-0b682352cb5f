// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';

contract DepletedAssetRepayLiquidity is Test {
    IAmmalgamPair private pair;
    address private pairAddress;

    address private tester;

    FactoryPairTestFixture private fixture;

    uint256 private initialMintX;
    uint256 private initialMintY;
    uint256 private testerMintX;
    uint256 private testerMintY;
    uint256 private missingXAssets;
    uint256 private missingYAssets;
    uint256 private initialLiquidity;
    uint256 private testerLiquidity;

    function setUp() public {
        tester = address(1111);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        pair = fixture.pair();
        fixture.updateExternalLiquidity(40e18);
        pairAddress = address(pair);

        initialMintX = 4e18;
        initialMintY = 4e18;

        fixture.transferTokensTo(tester, initialMintX, initialMintY);
        initialLiquidity = fixture.mintFor(tester, initialMintX, initialMintY);

        missingXAssets = 1e18;
        missingYAssets = 1e18;
        fixture.borrowFor(tester, missingXAssets, missingYAssets);
    }

    /**
     * @dev
     * initial reserves (4,4), changes to reserves(3,3) after borrowed 1 L
     * swap to move reserves from (3, 3) to (2.09, 5 + fee), the exhausted point is (2.105, 4.275);
     *
     * repay 1 L to move reserves to (2.4, 6.675) in range.
     * repayLX is calculated from complete replenish formula
     *
     * use this formula to get x amount of repay L :
     * L = 1 = (amountAssets + reserveAssets) * (100 - BUFFER) * activeLiquidity_ / 100 / (reserveAssets - missingAssets) - activeLiquidity_
     * 1 = (x + 2.09) * (100 - 95) * 3 / 100 / (2.09 - 2) - 3
     * x + 2.09 =(1 + 3) * (2.09-2) *100 / (100-95) / 3 = 2.3999
     * x = 2.3999 - 2.09 = 0.309
     *
     * counter check x
     * 1 = (x + 2.09) * (100 - 95) * 3 / 100 / (2.09 - 2) - 3
     * L = (0.31  + 2.09) * (100 -95) * 3 / 100 / (2.09 -2) - 3 = 1
     *
     * so x = 0.3099
     * y = 1.667  (L = amountY * _activeLiquidity /  _reserveY )  => (amountY = L * _reserveY / _activeLiquidity)   1 = y * 3 / 5  =>   y = 5 / 3 = 1.6666
     */
    function testDepletedAssetXRepayLiquidityReplenish() public {
        uint256 borrowL = 1e18;
        uint256 moveToX = 2.09e18;

        verifyRepayLiquidityExhaustX(moveToX, borrowL);
    }

    /**
     * @dev
     * initial reserves (4,4), changes to reserves(3,3) after borrowed 1 L
     * swap to move reserves from (3, 3) to (2.07, 6.438), the exhausted point is (2.105, 4.275);
     * repay 1 L to move reserves to (2.093, 8.858) still in left buffer.
     * repayLX is calculated from partial replenish formula
     */
    function testDepletedAssetXRepayLiquidityPartialReplenish() public {
        uint256 borrowL = 1e18;
        uint256 moveToX = 2.07e18;

        verifyRepayLiquidityExhaustX(moveToX, borrowL);
    }

    function verifyRepayLiquidityExhaustX(uint256 moveToX, uint256 borrowL) private {
        (uint256 borrowLX, uint256 borrowLY) = fixture.borrowLiquidityFor(tester, borrowL);

        (, uint256 swapAmountXOut) = fixture.moveReservesLeftToXValue(tester, moveToX);

        (uint256 repayLX, uint256 repayLY) = fixture.computeAmountsForRepayLiquidity(borrowL);

        fixture.transferTokensTo(pairAddress, repayLX, repayLY);

        (uint256 reserveXBeforeRepay, uint256 reserveYBeforeRepay,) = pair.getReserves();

        pair.repayLiquidity(tester);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBeforeRepay + repayLX - missingXAssets,
                tokenY: reserveYBeforeRepay + repayLY - missingYAssets,
                reserveXAssets: reserveXBeforeRepay + repayLX,
                reserveYAssets: reserveYBeforeRepay + repayLY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectedAddress;

        expectedAddress.toCheck = tester;
        expectedAddress.balanceX = borrowLX + missingXAssets + swapAmountXOut;
        expectedAddress.balanceY = borrowLY + missingYAssets;
        expectedAddress.pairBorrowedX = missingXAssets;
        expectedAddress.pairBorrowedY = missingYAssets;
        expectedAddress.pairBorrowedL = 0;
        expectedAddress.pairLiquidity = Math.sqrt(initialMintX * initialMintX) - 1000;

        fixture.verifyAddress(expectedAddress);
    }

    /**
     * @dev
     * initial reserves (4,4), changes to reserves(3,3) after borrowed 1 L, the exhausted point is (4.275, 2.105);
     * swap to move reserves from (3, 3) to (4.5 + fee, 2.1), the exhausted point is (4.48, 2.105);
     * repay 1 L to move reserves to (6.0, 2.66) in range . new exhausted point is (7.41, 2.105);
     * repayLY is calculated from complete replenish formula
     */
    function testDepletedAssetYRepayLiquidityReplenish() public {
        uint256 borrowL = 1e18;
        uint256 moveToY = 2.1e18;

        verifyRepayLiquidityExhaustY(moveToY, borrowL);
    }

    /**
     * @dev
     * initial reserves (4,4), changes to reserves(3,3) after borrowed 1 L, the exhausted point is (4.275, 2.105);
     * swap to move reserves from (3, 3) to (6 + fee, 2.075), the exhausted point is (5.914, 2.105);
     * repay 1 L to move reserves to (8.0, 2.1) in right buffer. new exhausted point is (7.98, 2.105);
     * repayLY is calculated from partial replenish formula
     */
    function testDepletedAssetYRepayLiquidityPartialReplenish() public {
        uint256 borrowL = 1e18;
        uint256 moveToY = 2.075e18;

        verifyRepayLiquidityExhaustY(moveToY, borrowL);
    }

    function verifyRepayLiquidityExhaustY(uint256 moveToY, uint256 borrowL) private {
        (uint256 borrowLX, uint256 borrowLY) = fixture.borrowLiquidityFor(tester, borrowL);

        (, uint256 swapAmountYOut) = fixture.moveReservesRightToYValue(tester, moveToY);

        (uint256 repayLX, uint256 repayLY) = fixture.computeAmountsForRepayLiquidity(borrowL);

        fixture.transferTokensTo(pairAddress, repayLX, repayLY);

        (uint256 reserveXBeforeRepay, uint256 reserveYBeforeRepay,) = pair.getReserves();

        pair.repayLiquidity(tester);

        fixture.verifyPair(
            FactoryPairTestFixture.ExpectedPairState({
                tokenX: reserveXBeforeRepay + repayLX - missingXAssets,
                tokenY: reserveYBeforeRepay + repayLY - missingYAssets,
                reserveXAssets: reserveXBeforeRepay + repayLX,
                reserveYAssets: reserveYBeforeRepay + repayLY
            })
        );

        FactoryPairTestFixture.ExpectedAddressState memory expectedAddress;

        expectedAddress.toCheck = tester;
        expectedAddress.balanceX = borrowLX + missingXAssets;
        expectedAddress.balanceY = borrowLY + missingYAssets + swapAmountYOut;
        expectedAddress.pairBorrowedX = missingXAssets;
        expectedAddress.pairBorrowedY = missingYAssets;
        expectedAddress.pairBorrowedL = 0;
        expectedAddress.pairLiquidity = initialLiquidity;

        fixture.verifyAddress(expectedAddress);
    }
}
