// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.18;

import {Test} from 'forge-std/Test.sol';

import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';
import {IERC4626} from '@openzeppelin/contracts/interfaces/IERC4626.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {MathLib, WAD} from '@morpho-org/morpho-blue/src/libraries/MathLib.sol';

import {AmmalgamPair} from 'contracts/AmmalgamPair.sol';
import {IPairHarness} from 'test/shared/FactoryPairTestFixture.sol';
import {DEPOSIT_L, BORROW_L} from 'contracts/interfaces/tokens/ITokenController.sol';
import {TickMath} from 'contracts/libraries/TickMath.sol';
import {Interest} from 'contracts/libraries/Interest.sol';

import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {
    InterestFixture,
    SharesAndAssets,
    MintAndBurnInputParams,
    BorrowAndRepayLiquidityInputParams,
    UserPosition,
    RepayWithdrawPosition,
    RAY
} from 'test/InterestTests/InterestFixture.sol';

import {
    TOLERANCE_1,
    TOLERANCE_2,
    TOLERANCE_3,
    INTEREST_PERIOD_0,
    INTEREST_PERIOD_1,
    INTEREST_PERIOD_2,
    INTEREST_PERIOD_3,
    INTEREST_PERIOD_4,
    INTEREST_PERIOD_5
} from 'test/utils/constants.sol';
import {LIQUIDITY_INTEREST_RATE_MAGNIFICATION} from 'contracts/libraries/constants.sol';

contract InterestBorrowRepayLiquidityTests is Test {
    using MathLib for uint256;
    using MathLib for uint128;

    FactoryPairTestFixture public fixture;
    InterestFixture public interestFixture;
    IPairHarness public pair;

    address random = address(0xa0);
    address borrower1Addr = address(0xb1);
    address borrower2Addr = address(0xb2);
    address minter1Addr = address(0xd1);
    address minter2Addr = address(0xd2);

    UserPosition user1Position;
    UserPosition user2Position;

    SharesAndAssets states;

    uint256 initialLX = 100e18;
    uint256 initialLY = 400e18;
    uint256 initialL = 200e18;

    function setUp() public {
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        interestFixture = new InterestFixture(fixture);

        initPool(100e18, 400e18);

        // reset state to not pollute tests
        UserPosition memory blankStruct;
        user1Position = blankStruct;
        user2Position = blankStruct;

        user1Position.userAddress = minter1Addr;
        user1Position.mintLXAssets = 100e18;
        user1Position.mintLYAssets = 400e18;

        user2Position.userAddress = minter2Addr;
        user2Position.mintLXAssets = 300e18;
        user2Position.mintLYAssets = 1200e18;

        pair = fixture.pair();
    }

    function initPool(uint256 _initialLX, uint256 _initialLY) private {
        fixture.transferTokensTo(random, _initialLX, _initialLY);
        fixture.mintForAndInitializeBlocks(random, _initialLX, _initialLY);
    }

    function testLiquidityInterest_mintOnceBorrowAndRepay_BeforeBurn() public {
        UserPosition memory borrow1Position;
        borrow1Position.userAddress = borrower1Addr;
        borrow1Position.borrowLAssets = 30e18;
        borrow1Position.depositXAssets = type(uint112).max / 2;
        uint256 borrowedLX = borrow1Position.borrowLAssets * initialLX / initialL;
        uint256 borrowedLY = borrow1Position.borrowLAssets * initialLY / initialL;
        uint256 duration1 = 12;

        // create first two positions to start earning interest
        interestFixture.createUserPosition(user1Position);
        interestFixture.createUserPosition(borrow1Position);

        SharesAndAssets memory startingPairState = interestFixture.getPairStates();
        uint256 startingUser1DepositLAssets = pair.exposed_getAssets(DEPOSIT_L, user1Position.userAddress);

        assertEq(fixture.tokenX().balanceOf(borrower1Addr), borrowedLX, 'borrowLX balance mismatch');
        assertEq(fixture.tokenY().balanceOf(borrower1Addr), borrowedLY, 'borrowLY balance mismatch');

        uint256 expectedInterest1 =
            LIQUIDITY_INTEREST_RATE_MAGNIFICATION * interestFixture.warpAndComputeInterestAssets(duration1);

        uint256 protocolFeeL = expectedInterest1 * Interest.LENDING_FEE_RATE / 100;
        uint256 netExpectedInterest1 = expectedInterest1 - protocolFeeL;

        SharesAndAssets memory latestPairState = interestFixture.getPairStates();

        uint256 user1TotalDepositLShares = pair.tokens(DEPOSIT_L).balanceOf(user1Position.userAddress);
        uint256 afterUser1DepositLAssets = pair.exposed_getAssets(DEPOSIT_L, user1Position.userAddress);

        assertApproxEqRel(
            latestPairState.depositLAssets,
            startingPairState.depositLAssets + expectedInterest1,
            1,
            'total assets reflect accrued interest'
        );

        verifyShareToAssetRatioOnDeposits(
            user1TotalDepositLShares,
            latestPairState.depositLShares,
            afterUser1DepositLAssets,
            latestPairState.depositLAssets
        );

        // the user1 deposit is half of the total deposits.
        uint256 expectedUser1LAssets = startingUser1DepositLAssets + netExpectedInterest1 / 2;
        assertApproxEqRel(afterUser1DepositLAssets, expectedUser1LAssets, 1, 'user1 depositL Assets should match');

        RepayWithdrawPosition memory withdrawUser1Position;
        withdrawUser1Position.userAddress = user1Position.userAddress;
        withdrawUser1Position.burnLShares = user1TotalDepositLShares;

        interestFixture.repayWithdrawUserPosition(withdrawUser1Position);

        verifyBurnedLiquidityAssets(
            withdrawUser1Position.burnLShares,
            fixture.tokenX().balanceOf(user1Position.userAddress),
            fixture.tokenY().balanceOf(user1Position.userAddress)
        );

        latestPairState = interestFixture.getPairStates();

        RepayWithdrawPosition memory repayBorrowPosition;
        repayBorrowPosition.userAddress = borrow1Position.userAddress;
        repayBorrowPosition.repayBorrowLXAssets =
            Math.ceilDiv(borrowedLX * latestPairState.borrowLAssets, latestPairState.borrowLShares);
        repayBorrowPosition.repayBorrowLYAssets =
            Math.ceilDiv(borrowedLY * latestPairState.borrowLAssets, latestPairState.borrowLShares);

        assertEq(
            repayBorrowPosition.repayBorrowLXAssets,
            fixture.pair().exposed_getAssets(BORROW_L, repayBorrowPosition.userAddress) * latestPairState.reserveXAssets
                / (latestPairState.depositLAssets - latestPairState.borrowLAssets),
            'The amount of X to repay should match the the borrowed liquidity scaled by active reserves'
        );
        assertEq(
            repayBorrowPosition.repayBorrowLYAssets,
            fixture.pair().exposed_getAssets(BORROW_L, repayBorrowPosition.userAddress) * latestPairState.reserveYAssets
                / (latestPairState.depositLAssets - latestPairState.borrowLAssets),
            'The amount of Y to repay should match the the borrowed liquidity scaled by active reserves'
        );

        interestFixture.repayWithdrawUserPosition(repayBorrowPosition);

        assertEq(pair.tokens(BORROW_L).balanceOf(repayBorrowPosition.userAddress), 0, 'borrowL balance should be 0');
    }

    function testLiquidityInterest_mintTwiceCheckBurnBalance() public {
        UserPosition memory borrow1Position;
        borrow1Position.userAddress = borrower1Addr;
        borrow1Position.borrowLAssets = 30e18;
        borrow1Position.depositXAssets = type(uint112).max / 4;
        uint256 borrowedLX = borrow1Position.borrowLAssets * initialLX / initialL;
        uint256 borrowedLY = borrow1Position.borrowLAssets * initialLY / initialL;
        uint256 duration = 12;

        // create first two positions to start earning interest
        interestFixture.createUserPosition(user1Position);
        interestFixture.createUserPosition(borrow1Position);

        uint256 startingDepositLAssets = pair.totalAssets()[DEPOSIT_L];

        assertEq(fixture.tokenX().balanceOf(borrower1Addr), borrowedLX, 'borrowLX balance mismatch');
        assertEq(fixture.tokenY().balanceOf(borrower1Addr), borrowedLY, 'borrowLY balance mismatch');

        uint256 expectedInterest1 =
            LIQUIDITY_INTEREST_RATE_MAGNIFICATION * interestFixture.warpAndComputeInterestAssets(duration);
        uint256 protocolFeeL1 = expectedInterest1 * Interest.LENDING_FEE_RATE / 100;

        assertApproxEqRel(
            startingDepositLAssets + expectedInterest1, pair.totalAssets()[DEPOSIT_L], 1, 'total assets should match 1'
        );

        // create second position to start earning interest
        interestFixture.createUserPosition(user2Position);

        uint256 portionRandom = pair.exposed_getAssets(DEPOSIT_L, random);
        uint256 portion1 = pair.exposed_getAssets(DEPOSIT_L, user1Position.userAddress);
        uint256 portion2 = pair.exposed_getAssets(DEPOSIT_L, user2Position.userAddress);

        assertApproxEqRel(
            portionRandom + portion1 + portion2 + protocolFeeL1,
            pair.totalAssets()[DEPOSIT_L],
            1,
            'total assets should match 2'
        );

        uint256 sharesRandom = pair.tokens(DEPOSIT_L).balanceOf(random);
        uint256 shares1 = pair.tokens(DEPOSIT_L).balanceOf(user1Position.userAddress);
        uint256 shares2 = pair.tokens(DEPOSIT_L).balanceOf(user2Position.userAddress);

        uint256 protocolFeeL1Shares = protocolFeeL1 * fixture.computeScalerHelper(BORROW_L) / RAY;

        assertApproxEqRel(
            sharesRandom + shares1 + shares2 + protocolFeeL1Shares,
            pair.tokens(DEPOSIT_L).totalSupply(),
            1,
            'total shares should match 3'
        );

        assertApproxEqRel(portion2 * RAY / shares2, portion1 * RAY / shares1, 1, 'portion 1 should match portion 2');

        // Check withdraw of second mint matches the deposit
        RepayWithdrawPosition memory withdrawUser2Position;
        withdrawUser2Position.userAddress = user2Position.userAddress;
        withdrawUser2Position.burnLShares = pair.tokens(DEPOSIT_L).balanceOf(user2Position.userAddress);

        interestFixture.repayWithdrawUserPosition(withdrawUser2Position);
        uint256 user2tokenXBalance = fixture.tokenX().balanceOf(withdrawUser2Position.userAddress);
        uint256 user2tokenYBalance = fixture.tokenY().balanceOf(withdrawUser2Position.userAddress);

        // token balances should be close to but less than or equal to what was minted
        assertApproxEqRel(
            user2tokenXBalance, user2Position.mintLXAssets, 1, 'user2 balance should be near what they minted'
        );
        assertLe(
            user2tokenXBalance,
            user2Position.mintLXAssets,
            'user2 balance should be less than or equal to what was minted'
        );

        assertApproxEqRel(user2tokenYBalance, user2Position.mintLYAssets, 1, 'user2 mintLY balance mismatch');
        assertLe(
            user2tokenYBalance,
            user2Position.mintLYAssets,
            'user2 balance should be less than or equal to what was minted'
        );
    }

    function testLiquidityInterest_mintBorrowWarpBorrowCheckRepay() public {
        UserPosition memory borrow1Position;
        borrow1Position.userAddress = borrower1Addr;
        borrow1Position.borrowLAssets = 30e18;
        borrow1Position.depositXAssets = type(uint112).max / 4;

        uint256 duration = 12;

        // create first two positions to start earning interest
        interestFixture.createUserPosition(user1Position);
        interestFixture.createUserPosition(borrow1Position);

        SharesAndAssets memory startingPairState = interestFixture.getPairStates();

        interestFixture.warpAndComputeInterestAssets(duration);

        // borrow again with a new address
        UserPosition memory borrow2Position;
        borrow2Position.userAddress = borrower2Addr;
        borrow1Position.borrowLAssets = 30e18;
        borrow1Position.depositXAssets = type(uint112).max / 4;
        interestFixture.createUserPosition(borrow2Position);

        assertEq(
            pair.exposed_getAssets(BORROW_L, borrow2Position.userAddress),
            borrow2Position.borrowLAssets,
            'borrowL balance mismatch'
        );

        interestFixture.verifyRepayAmountsAndRepay(
            borrow2Position.userAddress,
            borrow2Position.borrowLAssets,
            fixture.tokenX().balanceOf(borrow2Position.userAddress),
            fixture.tokenY().balanceOf(borrow2Position.userAddress)
        );

        SharesAndAssets memory pairState = interestFixture.getPairStates();
        interestFixture.verifyRepayAmountsAndRepay(
            borrow1Position.userAddress,
            pair.exposed_getAssets(BORROW_L, borrow1Position.userAddress),
            fixture.tokenX().balanceOf(borrow1Position.userAddress) * pairState.borrowLAssets
                / startingPairState.borrowLAssets,
            fixture.tokenY().balanceOf(borrow1Position.userAddress) * pairState.borrowLAssets
                / startingPairState.borrowLAssets
        );
    }

    function testLiquidityInterest_mintTwiceBorrowAndRepay_BeforeBurn() public {
        UserPosition memory borrow1Position;
        borrow1Position.userAddress = borrower1Addr;
        borrow1Position.borrowLAssets = 30e18;
        borrow1Position.depositXAssets = type(uint112).max / 2;
        uint256 borrowedLX = borrow1Position.borrowLAssets * initialLX / initialL;
        uint256 borrowedLY = borrow1Position.borrowLAssets * initialLY / initialL;
        uint256[2] memory durations = [uint256(12), 24];

        // create first two positions to start earning interest
        interestFixture.createUserPosition(user1Position);
        interestFixture.createUserPosition(borrow1Position);

        SharesAndAssets memory startingPairState = interestFixture.getPairStates();

        assertEq(fixture.tokenX().balanceOf(borrower1Addr), borrowedLX, 'borrowLX balance mismatch');
        assertEq(fixture.tokenY().balanceOf(borrower1Addr), borrowedLY, 'borrowLY balance mismatch');

        uint256 expectedInterest1 =
            LIQUIDITY_INTEREST_RATE_MAGNIFICATION * interestFixture.warpAndComputeInterestAssets(durations[0]);

        // create second position to start earning interest
        interestFixture.createUserPosition(user2Position);
        SharesAndAssets memory latestPairState;
        uint256 user1TotalDepositLShares;

        {
            SharesAndAssets memory secondMintPairState = interestFixture.getPairStates();

            uint256 expectedInterest2 =
                LIQUIDITY_INTEREST_RATE_MAGNIFICATION * interestFixture.warpAndComputeInterestAssets(durations[1]);

            latestPairState = interestFixture.getPairStates();
            user1TotalDepositLShares = pair.tokens(DEPOSIT_L).balanceOf(user1Position.userAddress);

            uint256 afterUser1DepositLAssets = pair.exposed_getAssets(DEPOSIT_L, user1Position.userAddress);
            uint256 user2TotalDepositLShares = pair.tokens(DEPOSIT_L).balanceOf(user2Position.userAddress);
            uint256 afterUser2DepositLAssets = pair.exposed_getAssets(DEPOSIT_L, user2Position.userAddress);

            assertApproxEqRel(
                latestPairState.depositLAssets,
                secondMintPairState.depositLAssets + expectedInterest2,
                1,
                'total assets reflect accrued interest'
            );

            verifyShareToAssetRatioOnDeposits(
                user1TotalDepositLShares,
                latestPairState.depositLShares,
                afterUser1DepositLAssets,
                latestPairState.depositLAssets
            );

            verifyShareToAssetRatioOnDeposits(
                user2TotalDepositLShares,
                latestPairState.depositLShares,
                afterUser2DepositLAssets,
                latestPairState.depositLAssets
            );
            // random + user1Position + user2Position + expectedInterest1 + expectedInterest2
            assertApproxEqRel(
                latestPairState.depositLAssets,
                initialL + initialL + 600e18 + expectedInterest1 + expectedInterest2,
                1,
                'total assets should match the expected total assets'
            );
        }

        {
            RepayWithdrawPosition memory withdrawUser1Position;
            withdrawUser1Position.userAddress = user1Position.userAddress;
            withdrawUser1Position.burnLShares = user1TotalDepositLShares;

            interestFixture.repayWithdrawUserPosition(withdrawUser1Position);

            verifyBurnedLiquidityAssets(
                withdrawUser1Position.burnLShares,
                fixture.tokenX().balanceOf(withdrawUser1Position.userAddress),
                fixture.tokenY().balanceOf(withdrawUser1Position.userAddress)
            );

            RepayWithdrawPosition memory withdrawUser2Position;
            withdrawUser2Position.userAddress = user2Position.userAddress;

            interestFixture.repayWithdrawUserPosition(withdrawUser2Position);

            verifyBurnedLiquidityAssets(
                withdrawUser2Position.burnLShares,
                fixture.tokenX().balanceOf(withdrawUser2Position.userAddress),
                fixture.tokenY().balanceOf(withdrawUser2Position.userAddress)
            );
        }
        latestPairState = interestFixture.getPairStates();
        interestFixture.verifyRepayAmountsAndRepay(
            borrow1Position.userAddress,
            pair.exposed_getAssets(BORROW_L, borrow1Position.userAddress),
            Math.mulDiv(
                fixture.tokenX().balanceOf(borrow1Position.userAddress),
                latestPairState.borrowLAssets,
                startingPairState.borrowLAssets,
                Math.Rounding.Ceil
            ),
            Math.mulDiv(
                fixture.tokenY().balanceOf(borrow1Position.userAddress),
                latestPairState.borrowLAssets,
                startingPairState.borrowLAssets,
                Math.Rounding.Ceil
            )
        );
    }

    function testLiquidityInterest_borrowTwiceInterestCheckRepay() public {
        UserPosition memory borrow1Position;
        borrow1Position.userAddress = borrower1Addr;
        borrow1Position.borrowLAssets = 30e18;
        borrow1Position.depositXAssets = type(uint112).max / 3;
        UserPosition memory borrow2Position;
        borrow2Position.userAddress = borrower2Addr;
        borrow2Position.borrowLAssets = 60e18;
        borrow2Position.depositXAssets = type(uint112).max / 3;
        uint256 duration = 12;

        // create first two positions to start earning interest
        interestFixture.createUserPosition(borrow1Position);
        interestFixture.createUserPosition(borrow2Position);
        SharesAndAssets memory startingPairState = interestFixture.getPairStates();
        uint256 expectedInterest1 =
            LIQUIDITY_INTEREST_RATE_MAGNIFICATION * interestFixture.warpAndComputeInterestAssets(duration);

        SharesAndAssets memory latestPairState = interestFixture.getPairStates();

        assertApproxEqRel(
            startingPairState.borrowLAssets + expectedInterest1,
            latestPairState.borrowLAssets,
            1,
            'total assets should match'
        );

        // Repay borrow1
        interestFixture.verifyRepayAmountsAndRepay(
            borrow1Position.userAddress,
            pair.exposed_getAssets(BORROW_L, borrow1Position.userAddress),
            fixture.tokenX().balanceOf(borrow1Position.userAddress) * latestPairState.borrowLAssets
                / startingPairState.borrowLAssets,
            fixture.tokenY().balanceOf(borrow1Position.userAddress) * latestPairState.borrowLAssets
                / startingPairState.borrowLAssets
        );
        // Repay borrow2
        interestFixture.verifyRepayAmountsAndRepay(
            borrow2Position.userAddress,
            pair.exposed_getAssets(BORROW_L, borrow2Position.userAddress),
            fixture.tokenX().balanceOf(borrow2Position.userAddress) * latestPairState.borrowLAssets
                / startingPairState.borrowLAssets,
            fixture.tokenY().balanceOf(borrow2Position.userAddress) * latestPairState.borrowLAssets
                / startingPairState.borrowLAssets
        );
    }

    function testLiquidityInterest_BorrowAndRepay_MultiBorrows_PartialRepays() public {
        UserPosition memory borrow1Position;
        borrow1Position.userAddress = borrower1Addr;
        borrow1Position.borrowLAssets = 30e18;
        borrow1Position.depositXAssets = type(uint112).max / 3;
        UserPosition memory borrow2Position;
        borrow2Position.userAddress = borrower2Addr;
        borrow2Position.borrowLAssets = 60e18;
        borrow2Position.depositXAssets = type(uint112).max / 3;
        SharesAndAssets memory startingPairStateBorrow1;
        {
            uint256[3] memory durations = [uint256(12), uint256(64), uint256(365 days)];

            interestFixture.createUserPosition(user1Position);
            interestFixture.createUserPosition(borrow1Position);

            startingPairStateBorrow1 = interestFixture.getPairStates();

            interestFixture.warpAndComputeInterestAssets(durations[0]);

            interestFixture.createUserPosition(user2Position);
            interestFixture.createUserPosition(borrow2Position);

            interestFixture.warpAndComputeInterestAssets(durations[1]);
        }
        SharesAndAssets memory latestPairState = interestFixture.getPairStates();
        uint256 borrow2LiquidityAssets = pair.exposed_getAssets(BORROW_L, borrow2Position.userAddress);

        // repayLiquidity 1
        // 50% repay
        interestFixture.verifyRepayAmountsAndRepay(
            borrow1Position.userAddress,
            pair.exposed_getAssets(BORROW_L, borrow1Position.userAddress) * 50 / 100,
            fixture.tokenX().balanceOf(borrow1Position.userAddress)
                * (latestPairState.borrowLAssets - borrow2LiquidityAssets) * 50 / startingPairStateBorrow1.borrowLAssets
                / 100,
            fixture.tokenY().balanceOf(borrow1Position.userAddress)
                * (latestPairState.borrowLAssets - borrow2LiquidityAssets) * 50 / startingPairStateBorrow1.borrowLAssets
                / 100,
            pair.tokens(BORROW_L).balanceOf(borrow1Position.userAddress) * 50 / 100
        );
    }

    function verifyBurnedLiquidityAssets(
        uint256 burnShares,
        uint256 actualBurnedLX,
        uint256 actualBurnedLY
    ) private view {
        SharesAndAssets memory afterPairState = interestFixture.getPairStates();

        // we scale user shares by total assets and then again to reserves with respect to active assets.
        // userShares * totalAssets / totalShares * reserve / (depositAssets - borrowAssets)
        // userShares * totalAssets * reserve / ( totalShares * (depositAssets - borrowAssets))
        uint256 expectedBurnedLX = Math.mulDiv(
            burnShares * afterPairState.depositLAssets,
            afterPairState.reserveXAssets,
            afterPairState.depositLShares * (afterPairState.depositLAssets - afterPairState.borrowLAssets)
        );
        uint256 expectedBurnedLY = Math.mulDiv(
            burnShares * afterPairState.depositLAssets,
            afterPairState.reserveYAssets,
            afterPairState.depositLShares * (afterPairState.depositLAssets - afterPairState.borrowLAssets)
        );
        assertApproxEqAbs(
            actualBurnedLX, expectedBurnedLX, 1, 'verifyBurnedLiquidityAssets user1 burnLXAssets should match expected'
        );
        assertApproxEqAbs(
            actualBurnedLY, expectedBurnedLY, 1, 'verifyBurnedLiquidityAssets user1 burnLYAssets should match expected'
        );
    }

    function verifyShareToAssetRatioOnDeposits(
        uint256 userShares,
        uint256 totalShares,
        uint256 userAssets,
        uint256 totalAssets
    ) public pure {
        // Verify userShares / totalShares == userAssets / totalAssets
        assertEq(
            userShares * WAD / totalShares,
            userAssets * WAD / totalAssets,
            'user should get slightly less assets than his shares'
        );
        assertLe(
            (userShares - 1) * WAD / totalShares,
            userAssets * WAD / totalAssets,
            'user should not get less assets than his shares minus 1'
        );
    }
}
