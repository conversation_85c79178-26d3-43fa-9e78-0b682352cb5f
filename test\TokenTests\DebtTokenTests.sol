// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';

import {IERC20} from '@openzeppelin/contracts/interfaces/IERC20.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';
import {ERC4626} from '@openzeppelin/contracts/token/ERC20/extensions/ERC4626.sol';

import {IAmmalgamERC20} from 'contracts/interfaces/tokens/IAmmalgamERC20.sol';
import {IERC20DebtToken} from 'contracts/interfaces/tokens/IERC20DebtToken.sol';
import {ICallback} from 'contracts/interfaces/callbacks/IAmmalgamCallee.sol';
import {ERC20DebtBase} from 'contracts/tokens/ERC20DebtBase.sol';
import {
    BORROW_L,
    BORROW_X,
    BORROW_Y,
    DEPOSIT_L,
    DEPOSIT_X,
    DEPOSIT_Y
} from 'contracts/interfaces/tokens/ITokenController.sol';
import {Validation} from 'contracts/libraries/Validation.sol';
import {ERC20BaseConfig} from 'contracts/tokens/ERC20Base.sol';

import {FactoryPairTestFixture, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {IPairHarness} from 'test/shared/FactoryPairTestFixture.sol';

contract DebtTokenTests is Test {
    FactoryPairTestFixture private fixture;
    address private random;
    address private tester;
    address private tester2;
    address private pairAddress;
    uint256 private initialX = 1000e18;
    uint256 private initialY = 1000e18;

    error ERC20InsufficientAllowance(address spender, uint256 allowance, uint256 needed);

    function setUp() public {
        random = vm.addr(99);
        tester = vm.addr(100);
        tester2 = vm.addr(101);
        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, false, false);
        fixture.transferTokensTo(random, initialX, initialY);
        fixture.mintForAndInitializeBlocks(random, initialX, initialY);
        fixture.updateExternalLiquidity(type(uint112).max - 2000e18);
        pairAddress = fixture.pairAddress();
    }

    function testConsistencyOfRounding() public {
        // test based on ChainSecurity CS-AMMALGAM-008

        fixture.transferTokensTo(tester2, 1e18, 0);
        fixture.depositFor(tester2, 1e18, 0);
        fixture.borrowFor(tester2, 0, 1e17);
        fixture.mineBlock(block.number + 1, block.timestamp + 1 days);
        fixture.pair().sync();

        fixture.transferTokensTo(tester, 1e18, 0);
        fixture.depositFor(tester, 1e18, 0);

        vm.startPrank(tester);
        // would revert with AmmalgamDepositIsNotStrictlyBigger if rounding inconsistent
        ERC4626(address(fixture.pair().tokens(BORROW_Y))).deposit(1e17, tester);
        vm.stopPrank();
    }

    function testWithdrawXWithDebtInY() public {
        uint256 depositAmountX = 100e18;
        fixture.transferTokensTo(tester, depositAmountX, 0);
        fixture.depositFor(tester, depositAmountX, 0);

        uint256 borrowAmountY = 1;
        fixture.borrowFor(tester, 0, borrowAmountY);

        uint256 withdrawAmountX = depositAmountX;

        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        fixture.withdrawFor(tester, withdrawAmountX, 0);
    }

    function testWithdrawYWithDebtInX() public {
        uint256 depositAmountY = 100e18;
        fixture.transferTokensTo(tester, 0, depositAmountY);
        fixture.depositFor(tester, 0, depositAmountY);

        uint256 borrowAmountX = 1;
        fixture.borrowFor(tester, borrowAmountX, 0);

        uint256 withdrawAmountY = depositAmountY;

        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        fixture.withdrawFor(tester, 0, withdrawAmountY);
    }

    function testWithdrawYWithDebtInL() public {
        uint256 depositAmountY = 100e18;
        fixture.transferTokensTo(tester, 0, depositAmountY);
        fixture.depositFor(tester, 0, depositAmountY);

        uint256 borrowL = 1;
        fixture.borrowLiquidityFor(tester, borrowL);

        uint256 withdrawAmountY = depositAmountY;

        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        fixture.withdrawFor(tester, 0, withdrawAmountY);
    }

    function testBurnLWithDebtInY() public {
        uint256 mintX = 100e18;
        uint256 mintY = 100e18;
        uint256 mintedL = Math.sqrt(mintX * mintY);

        fixture.transferTokensTo(tester, mintX, mintY);
        fixture.mintFor(tester, mintX, mintY);

        uint256 borrowAmountY = 1;
        fixture.borrowFor(tester, 0, borrowAmountY);

        uint256 burnL = mintedL;

        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        fixture.burnFor(tester, burnL);
    }

    function testBurnLWithDebtInX() public {
        uint256 mintX = 100e18;
        uint256 mintY = 100e18;
        uint256 mintedL = Math.sqrt(mintX * mintY);

        fixture.transferTokensTo(tester, mintX, mintY);
        fixture.mintFor(tester, mintX, mintY);

        uint256 borrowAmountX = 1;
        fixture.borrowFor(tester, borrowAmountX, 0);

        uint256 burnL = mintedL;

        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        fixture.burnFor(tester, burnL);
    }

    function testBurnLWithDebtInL() public {
        uint256 mintX = 100e18;
        uint256 mintY = 100e18;
        uint256 mintedL = Math.sqrt(mintX * mintY);

        fixture.transferTokensTo(tester, mintX, mintY);
        fixture.mintFor(tester, mintX, mintY);

        // balance is higher than minted
        uint256 balance = IPairHarness(pairAddress).exposed_getAssets(0, tester);
        assertEq(balance, mintedL, 'balance should be equal to mintedL');

        uint256 borrowL = 1;
        fixture.borrowLiquidityFor(tester, borrowL);

        uint256 burnL = mintedL;

        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        fixture.burnFor(tester, burnL);
    }

    function testTransferDebtByOwner() public {
        uint256 borrowAmountY = createDebtPositionY(tester);

        IERC20DebtToken iDebtToken = getDebtToken(BORROW_Y);

        vm.startPrank(tester2);
        iDebtToken.approveDebt(tester, borrowAmountY);
        vm.stopPrank();

        // deposit for tester2 as collateral
        uint256 depositAmountX = 100e18;
        fixture.transferTokensTo(tester2, depositAmountX, 0);
        fixture.depositFor(tester2, depositAmountX, 0);

        vm.startPrank(tester);
        iDebtToken.transfer(tester2, borrowAmountY);
        vm.stopPrank();

        assertEq(iDebtToken.balanceOf(tester2), borrowAmountY);
        assertEq(iDebtToken.balanceOf(tester), 0);
    }

    function testTransferFromByContract() public {
        uint256 borrowAmountY = createDebtPositionY(tester);

        IERC20DebtToken iDebtToken = getDebtToken(BORROW_Y);

        // tester2 approves 3rd party contract to transfer debt
        vm.startPrank(tester2);
        iDebtToken.approveDebt(address(this), borrowAmountY);
        vm.stopPrank();

        // deposit for tester2 as collateral
        uint256 depositAmountX = 100e18;
        fixture.transferTokensTo(tester2, depositAmountX, 0);
        fixture.depositFor(tester2, depositAmountX, 0);

        // address(this) executes the transfer from tester to tester2
        iDebtToken.transferFrom(tester, tester2, borrowAmountY);

        assertEq(iDebtToken.balanceOf(tester2), borrowAmountY);
        assertEq(iDebtToken.balanceOf(tester), 0);
        assertEq(iDebtToken.balanceOf(address(this)), 0);
    }

    function testTransferFromByReceiver() public {
        uint256 borrowAmountY = createDebtPositionY(tester);

        IERC20DebtToken iDebtToken = getDebtToken(BORROW_Y);

        vm.startPrank(tester2);
        iDebtToken.approveDebt(tester, borrowAmountY);
        vm.stopPrank();

        // deposit for tester2 as collateral
        uint256 depositAmountX = 100e18;
        fixture.transferTokensTo(tester2, depositAmountX, 0);
        fixture.depositFor(tester2, depositAmountX, 0);

        vm.startPrank(tester2);
        iDebtToken.approveDebt(tester2, borrowAmountY);
        iDebtToken.transferFrom(tester, tester2, borrowAmountY);
        vm.stopPrank();

        assertEq(iDebtToken.balanceOf(tester2), borrowAmountY);
        assertEq(iDebtToken.balanceOf(tester), 0);
    }

    function testTransferLiquidityDebtByContract() public {
        uint256 borrowAmountL = createDebtPositionL(tester);

        IERC20DebtToken iDebtToken = getDebtToken(BORROW_L);

        // tester2 approves 3rd party contract to transfer debt
        vm.startPrank(tester2);
        iDebtToken.approveDebt(address(this), borrowAmountL);
        vm.stopPrank();

        // deposit for tester2 as collateral
        uint256 depositAmountX = 100e18;
        fixture.transferTokensTo(tester2, depositAmountX, 0);
        fixture.depositFor(tester2, depositAmountX, 0);

        // address(this) executes the transfer from tester to tester2
        iDebtToken.transferFrom(tester, tester2, borrowAmountL);

        assertEq(iDebtToken.balanceOf(tester2), borrowAmountL);
        assertEq(iDebtToken.balanceOf(tester), 0);
    }

    function testTransferDebtByOwnerToReceiverShouldFail() public {
        uint256 borrowAmountY = createDebtPositionY(tester);

        IERC20DebtToken iDebtToken = getDebtToken(BORROW_Y);

        vm.startPrank(tester);
        iDebtToken.approveDebt(tester2, borrowAmountY);
        vm.expectRevert(abi.encodeWithSelector(ERC20InsufficientAllowance.selector, tester, 0, borrowAmountY));
        iDebtToken.transfer(tester2, borrowAmountY);
        vm.stopPrank();
    }

    function testTransferWithoutDepositShouldFail() public {
        uint256 borrowAmountY = createDebtPositionY(tester);

        IERC20DebtToken iDebtToken = getDebtToken(BORROW_Y);

        vm.startPrank(tester2);

        iDebtToken.approveDebt(tester2, borrowAmountY);
        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        iDebtToken.transferFrom(tester, tester2, borrowAmountY);

        vm.stopPrank();
    }

    function testTransferDebtLessThanReceiversCollateral() public {
        uint256 borrowAmountY = createDebtPositionY(tester);

        IERC20DebtToken iDebtToken = getDebtToken(BORROW_Y);

        // deposit for tester2 as collateral
        uint256 depositAmountX = 100e18;
        fixture.transferTokensTo(tester2, depositAmountX, 0);
        fixture.depositFor(tester2, depositAmountX, 0);
        uint256 borrowAmountY2 = 50e18;
        fixture.borrowFor(tester2, 0, borrowAmountY2);

        vm.startPrank(tester2);
        iDebtToken.approveDebt(tester2, borrowAmountY);
        iDebtToken.transferFrom(tester, tester2, borrowAmountY); // total debt = 50e18 + 1e18  still less than 100e18

        vm.stopPrank();

        assertEq(iDebtToken.balanceOf(tester), 0);
        assertEq(iDebtToken.balanceOf(tester2), borrowAmountY + borrowAmountY2);
    }

    function testTransferDebtOverReceiversCollateralShouldFail() public {
        uint256 borrowAmountY = createDebtPositionY(tester);

        IERC20DebtToken iDebtToken = getDebtToken(BORROW_Y);

        // deposit for tester2 as collateral
        uint256 depositAmountX = 100e18;
        fixture.transferTokensTo(tester2, depositAmountX, 0);
        fixture.depositFor(tester2, depositAmountX, 0);
        uint256 borrowAmountY2 = 74.5e18;
        fixture.borrowFor(tester2, 0, borrowAmountY2);

        vm.startPrank(tester2);
        iDebtToken.approveDebt(tester2, borrowAmountY);
        vm.expectRevert(Validation.AmmalgamLTV.selector);
        iDebtToken.transferFrom(tester, tester2, borrowAmountY);
        vm.stopPrank();
    }

    function testTransferDebtOnlyOwner() public {
        uint256 borrowAmountY = createDebtPositionY(tester);

        IERC20DebtToken iDebtToken = getDebtToken(BORROW_Y);

        // deposit for tester2 as collateral
        uint256 depositAmountX = 100e18;
        fixture.transferTokensTo(tester2, depositAmountX, 0);
        fixture.depositFor(tester2, depositAmountX, 0);
        uint256 borrowAmountY2 = 74.5e18; // decrease slightly from 75 for slippage
        fixture.borrowFor(tester2, 0, borrowAmountY2);

        vm.startPrank(tester2);
        iDebtToken.approveDebt(tester2, borrowAmountY);
        vm.expectRevert(Validation.AmmalgamLTV.selector);
        iDebtToken.transferFrom(tester, tester2, borrowAmountY);
        vm.stopPrank();
    }

    function testTransferDebtTokenYShouldFail() public {
        uint256 borrowAmountY = createDebtPositionY(tester);

        IERC20DebtToken iDebtToken = getDebtToken(BORROW_Y);
        vm.startPrank(tester);
        vm.expectRevert(abi.encodeWithSelector(ERC20InsufficientAllowance.selector, tester, 0, borrowAmountY));
        iDebtToken.transfer(pairAddress, borrowAmountY);
        vm.stopPrank();
    }

    function testTransferDebtTokenXShouldFail() public {
        uint256 borrowAmountX = createDebtPositionX(tester);

        IERC20DebtToken iDebtToken = getDebtToken(BORROW_X);
        vm.startPrank(tester);
        vm.expectRevert(abi.encodeWithSelector(ERC20InsufficientAllowance.selector, tester, 0, borrowAmountX));
        iDebtToken.transfer(pairAddress, borrowAmountX);
        vm.stopPrank();
    }

    function testTransferDebtTokenLShouldFail() public {
        uint256 borrowL = createDebtPositionL(tester);

        IERC20DebtToken iDebtToken = getDebtToken(BORROW_L);
        vm.startPrank(tester);
        vm.expectRevert(abi.encodeWithSelector(ERC20InsufficientAllowance.selector, tester, 0, borrowL));
        iDebtToken.transfer(pairAddress, borrowL);
        vm.stopPrank();
    }

    function testApproveShouldFailStandardERC20() public {
        IERC20DebtToken iDebtToken = getDebtToken(BORROW_X);
        vm.expectRevert(ERC20DebtBase.DebtERC20ApproveDebt.selector);
        uint256 borrowAmount = 1e18;
        iDebtToken.approve(tester, borrowAmount);
    }

    function testClaimDebt() public {
        uint256 borrowAmountY = createDebtPositionY(tester);
        IERC20DebtToken iDebtToken = getDebtToken(BORROW_Y);

        // deposit for tester2 as collateral
        uint256 depositAmountX = 100e18;
        fixture.transferTokensTo(tester2, depositAmountX, 0);
        fixture.depositFor(tester2, depositAmountX, 0);

        vm.prank(tester2);
        iDebtToken.claimDebt(tester, borrowAmountY);

        assertEq(iDebtToken.balanceOf(tester), 0);
        assertEq(iDebtToken.balanceOf(tester2), borrowAmountY);
    }

    /**
     * @dev test contract for `testContractTransferFromWillNotWork`.
     */
    TransferFromAnything transferFromAnything = new TransferFromAnything();

    function testContractTransferFromWillNotWork() public {
        uint256 largeContractDepositX = 1000e18;
        uint256 attackersDepositX = 1000e18;
        uint256 attackerBorrowY = 1e18;
        address attacker = tester;

        IERC20 pairDepositXToken = fixture.pair().tokens(DEPOSIT_X);
        IERC20 pairDebtYToken = fixture.pair().tokens(BORROW_Y);

        // transfer initial assets to random wallet and attacker wallet.
        fixture.transferTokensTo(random, largeContractDepositX, 0);
        fixture.transferTokensTo(attacker, attackersDepositX, 0);
        fixture.depositFor(random, largeContractDepositX, 0);
        fixture.depositFor(attacker, attackersDepositX, 0);

        // transfer from random address to contract to ensure
        // `validateSolvency` does not fail during debt token transfer
        vm.startPrank(random);
        pairDepositXToken.approve(address(transferFromAnything), largeContractDepositX);
        transferFromAnything.takeTokens(pairDepositXToken, largeContractDepositX);
        vm.stopPrank();

        // create debt position for attacker
        fixture.borrowFor(attacker, 0, attackerBorrowY);

        // verify a contract using `transferFrom` can not work without approval.
        vm.startPrank(attacker);
        vm.expectRevert(
            abi.encodeWithSelector(ERC20InsufficientAllowance.selector, DEFAULT_TEST_CONTRACT, 0, attackerBorrowY)
        );
        transferFromAnything.takeTokens(pairDebtYToken, attackerBorrowY);
    }

    function testClaimDebtWithoutCollateralShouldFail() public {
        uint256 borrowAmountY = createDebtPositionY(tester);
        IERC20DebtToken iDebtToken = getDebtToken(BORROW_Y);

        vm.expectRevert(Validation.AmmalgamDepositIsNotStrictlyBigger.selector);
        iDebtToken.claimDebt(tester, borrowAmountY);
    }

    function createDebtPositionY(
        address _tester
    ) private returns (uint256 borrowAmountY) {
        uint256 depositAmountX = 100e18;
        fixture.transferTokensTo(_tester, depositAmountX, 0);
        fixture.depositFor(_tester, depositAmountX, 0);
        borrowAmountY = 1e18;
        fixture.borrowFor(_tester, 0, borrowAmountY);
    }

    function createDebtPositionX(
        address _tester
    ) private returns (uint256 borrowAmountX) {
        uint256 depositAmountY = 100e18;
        fixture.transferTokensTo(_tester, 0, depositAmountY);
        fixture.depositFor(_tester, 0, depositAmountY);
        borrowAmountX = 1e18;
        fixture.borrowFor(_tester, borrowAmountX, 0);
    }

    function createDebtPositionL(
        address _tester
    ) private returns (uint256 borrowAmountL) {
        uint256 depositAmountX = 100e18;
        uint256 depositAmountY = 100e18;
        fixture.transferTokensTo(_tester, depositAmountX, depositAmountY);
        fixture.depositFor(_tester, depositAmountX, depositAmountY);
        borrowAmountL = 1e18;
        fixture.borrowLiquidityFor(_tester, borrowAmountL);
    }

    function getDebtToken(
        uint256 tokenType
    ) private view returns (IERC20DebtToken) {
        IAmmalgamERC20 ammToken = fixture.pair().tokens(tokenType);
        return IERC20DebtToken(address(ammToken));
    }
}

contract TransferFromAnything {
    function takeTokens(IERC20 token, uint256 amount) public {
        token.transferFrom(msg.sender, address(this), amount);
    }
}
